import { chainConfig } from '../../op-stack/chainConfig.js'
import { define<PERSON>hain } from '../../utils/chain/defineChain.js'

const sourceId = 1 // mainnet

export const zora = /*#__PURE__*/ defineChain({
  ...chainConfig,
  id: 7777777,
  name: '<PERSON><PERSON>',
  nativeCurrency: {
    decimals: 18,
    name: 'Ether',
    symbol: 'ETH',
  },
  rpcUrls: {
    default: {
      http: ['https://rpc.zora.energy'],
      webSocket: ['wss://rpc.zora.energy'],
    },
  },
  blockExplorers: {
    default: {
      name: 'Explorer',
      url: 'https://explorer.zora.energy',
      apiUrl: 'https://explorer.zora.energy/api',
    },
  },
  contracts: {
    ...chainConfig.contracts,
    l2OutputOracle: {
      [sourceId]: {
        address: '******************************************',
      },
    },
    multicall3: {
      address: '******************************************',
      blockCreated: 5882,
    },
    portal: {
      [sourceId]: {
        address: '******************************************',
      },
    },
    l1StandardBridge: {
      [sourceId]: {
        address: '******************************************',
      },
    },
  },
  sourceId,
})
