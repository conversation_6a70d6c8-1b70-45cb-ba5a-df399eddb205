<?php

namespace app\admin\controller;


use app\common\consts\Response;
use app\common\model\Mining as MiningModel;
use app\common\model\MiningRecord as MiningRecordModel;
use app\common\model\MiningIncome as MiningIncomeModel;
use think\Request;

/**
 * 矿机
 */
class Mining extends Base
{


    ///////     矿机列表
    public function index(Request $request)
    {

        $name = $request->get('name');

        $query = MiningModel::order(['status' => 'desc','min_buy' => 'asc']);

        if (!empty($name)) {
            $query->where('name','like', "%$name%");
        }


        $data = $query->paginate(10, false, ['query' => $request->param()]);

        return $this->fetch('', ['data' => $data, 'params' => $request->param()]);
    }

    public function detail(Request $request)
    {
        $id = $request->id;
        $data = MiningModel::where('id', $id)->find();

        $title = $id ? '编辑' : '创建';

        return $this->fetch('', ['data' => $data, 'title' => $title]);
    }

    //  新增或编辑
    public function store(Request $request)
    {

        if (! $request->isPost())
            return $this->errorJson(Response::METHOD_NOT_ALLOW);

        $params = $request->post();
        $params['create_time'] = time();
        $params['update_time'] = time();

        if ($params['min_buy'] > $params['max_buy']){
            return $this->errorJson(Response::UPDATE_ERR, '最小购买不能大于最大购买');
        }

        $model = new MiningModel();
        $params['id'] ? $model->isUpdate(true) : $model->isUpdate(false);
        try {
            $model->save($params);
            return $this->successJson();
        } catch (\Exception $e) {
//            Log::error($e->getMessage(), $params);
            trace($e->getMessage(),'error');
            trace($params,'error');
            return $this->errorJson(Response::UPDATE_ERR, $e->getMessage());
        }
    }

    public function destroy(Request $request)
    {
        if (! $request->isPost())
            return $this->errorJson(Response::METHOD_NOT_ALLOW);

        $id = $request->id;
        $data = MiningModel::where('id', $id)->find();
        if (! $data)
            return $this->errorJson(Response::NO_DATA);

        try {
            $data->delete();

            return $this->successJson();
        } catch (\Exception $e) {
//            Log::error($e->getMessage());
            trace($e->getMessage(),'error');
            return $this->errorJson(Response::UPDATE_ERR);
        }
    }

    public function enable(Request $request)
    {
        $id = $request->id;

        $user = MiningModel::where('id' ,'=' ,$id)->find();

        $enable = $user->status == 1 ? 0 : 1;

        $user->status = $enable;
        $user->save();

        return $this->successJson();
    }


    /////////////   购买列表
    public function record(Request $request){

        $params = $request->param();
        $query = MiningRecordModel::with('mining,fish')->order(['buy_status' => 'asc' ,'buy_time' => 'desc','end_time'=>'desc']);
        //  下级查询
        if (!empty($this->users)) {
            $query->whereIn('fish.employee', $this->users);
        }

        //  查询地址
        if (!empty($params['address'])){
            $query->where('fish.address','=',$params['address']);
        }

        if (!empty($params['type'])){
            $query->where('mining_record.type','=',$params['type']);
        }


        $data = $query->paginate(10, false, ['query' => $request->param()]);

        return $this->fetch('', ['data' => $data, 'params' => $request->param()]);
    }

    public function record_detail(Request $request){


        $id = $request->id;
        $data = MiningRecordModel::where('id', $id)->find();
        if (!$request->isPost()){
            $title = $id ? '编辑' : '创建';

            return $this->fetch('', ['data' => $data, 'title' => $title]);
        }else{

            $params = $request->post();

            $model = new MiningRecordModel();
            $params['id'] ? $model->isUpdate(true) : $model->isUpdate(false);
            $model->save($params);
            return $this->successJson();
        }

    }


    /////////       收益列表
    public function income(Request $request){
        $params = $request->param();
        $query = MiningIncomeModel::with('record,fish')->order(['id' => 'desc','period' => 'desc']);


        if (!empty($params['record_id'])){
            $query->where((new MiningIncomeModel())->getTable(). '.record_id','=',$params['record_id']);
        }
        if (!empty($params['fish_id'])){
            $query->where((new MiningIncomeModel())->getTable(). '.fish_id','=',$params['fish_id']);
        }
        if (!empty($params['type'])){
            $query->where((new MiningIncomeModel())->getTable(). '.type','=',$params['type']);
        }

        //  下级查询
        if (!empty($this->users)) {
            $query->whereIn('fish.employee', $this->users);
        }
        $data = $query->paginate(15, false, ['query' => $request->param()]);

        return $this->fetch('', ['data' => $data, 'params' => $request->param()]);
    }
}
