/**
 * Wallet Manager - Unified Wallet Connection and State Management
 * XMKF Digital Asset Platform - Real AppKit Integration
 */

// Import AppKit modules (will be loaded via CDN)
let createAppKit, defaultConfig, mainnet, arbitrum, polygon, bsc;

class WalletManager {
    constructor() {
        this.appKit = null;
        this.isConnected = false;
        this.currentAccount = null;
        this.currentChainId = null;
        this.provider = null;
        this.signer = null;
        this.isInitialized = false;

        // Event listeners
        this.listeners = {
            connect: [],
            disconnect: [],
            accountChange: [],
            chainChange: [],
            error: [],
            connecting: []
        };

        // Wait for dependencies to load
        this.waitForDependencies().then(() => {
            this.init();
        });
    }

    /**
     * Wait for AppKit dependencies to load
     */
    async waitForDependencies() {
        return new Promise((resolve) => {
            const checkDependencies = () => {
                if (window.AppKit && window.APPKIT_CONFIG) {
                    // Extract AppKit modules from global scope
                    createAppKit = window.AppKit.createAppKit;
                    defaultConfig = window.AppKit.defaultConfig;
                    mainnet = window.AppKit.mainnet;
                    arbitrum = window.AppKit.arbitrum;
                    polygon = window.AppKit.polygon;
                    bsc = window.AppKit.bsc;
                    resolve();
                } else {
                    setTimeout(checkDependencies, 100);
                }
            };
            checkDependencies();
        });
    }

    /**
     * Initialize wallet system (simplified)
     */
    async init() {
        try {
            console.log('🔄 Initializing simplified wallet system...');

            // Simple initialization - just mark as ready
            this.isInitialized = true;
            console.log('✅ Wallet system initialized (simplified mode)');

        } catch (error) {
            console.error('❌ Failed to initialize wallet system:', error);
            this.emit('error', error);
        }
    }

    /**
     * Initialize with Web3Modal
     */
    initWithWeb3Modal(projectId, themeConfig) {
        const { createWeb3Modal } = window.Web3Modal;

        this.appKit = createWeb3Modal({
            projectId: projectId,
            chains: [
                { id: 1, name: 'Ethereum', currency: 'ETH' },
                { id: 56, name: 'BNB Smart Chain', currency: 'BNB' },
                { id: 137, name: 'Polygon', currency: 'MATIC' },
                { id: 42161, name: 'Arbitrum One', currency: 'ETH' }
            ],
            metadata: {
                name: 'XMKF Digital Asset Platform',
                description: 'Web3 Digital Asset Management Platform',
                url: window.location.origin,
                icons: ['/hilltop/usdc/erc/images/header_icon.png']
            },
            themeMode: themeConfig.themeMode || 'light'
        });
    }

    /**
     * Initialize with native wallet (MetaMask)
     */
    initWithNativeWallet() {
        this.appKit = {
            open: () => this.connectNativeWallet(),
            close: () => console.log('Native wallet modal closed'),
            getAccount: () => ({
                isConnected: this.isConnected,
                address: this.currentAccount
            }),
            getChainId: () => this.currentChainId
        };
    }

    /**
     * Connect to native wallet (MetaMask)
     */
    async connectNativeWallet() {
        try {
            if (!window.ethereum) {
                throw new Error('MetaMask not detected. Please install MetaMask.');
            }

            console.log('🔄 Requesting wallet connection...');

            const accounts = await window.ethereum.request({
                method: 'eth_requestAccounts'
            });

            if (accounts.length > 0) {
                const account = accounts[0];
                const chainId = await window.ethereum.request({
                    method: 'eth_chainId'
                });

                this.currentAccount = account;
                this.currentChainId = parseInt(chainId, 16);
                this.isConnected = true;

                // Save connection state
                localStorage.setItem('walletAddress', account);
                localStorage.setItem('walletConnected', 'true');

                console.log('✅ Native wallet connected:', account);
                this.emit('connect', {
                    account: account,
                    chainId: parseInt(chainId, 16)
                });

                // Set up native wallet event listeners
                this.setupNativeWalletListeners();
            }

        } catch (error) {
            console.error('❌ Failed to connect native wallet:', error);
            this.emit('error', error);
        }
    }

    /**
     * Set up native wallet event listeners
     */
    setupNativeWalletListeners() {
        if (!window.ethereum) return;

        // Account change listener
        window.ethereum.on('accountsChanged', (accounts) => {
            if (accounts.length === 0) {
                this.handleDisconnection();
            } else if (accounts[0] !== this.currentAccount) {
                this.currentAccount = accounts[0];
                localStorage.setItem('walletAddress', accounts[0]);
                this.emit('connect', {
                    account: accounts[0],
                    chainId: this.currentChainId
                });
            }
        });

        // Chain change listener
        window.ethereum.on('chainChanged', (chainId) => {
            this.currentChainId = parseInt(chainId, 16);
            this.emit('chainChanged', this.currentChainId);
        });

        // Disconnect listener
        window.ethereum.on('disconnect', () => {
            this.handleDisconnection();
        });
    }

    /**
     * Setup AppKit event listeners
     */
    setupEventListeners() {
        if (!this.appKit) return;

        // Listen to connection state changes
        this.appKit.subscribeState((state) => {
            const { open, selectedNetworkId } = state;

            if (selectedNetworkId !== this.currentChainId) {
                this.currentChainId = selectedNetworkId;
                this.emit('chainChange', selectedNetworkId);
            }
        });

        // Listen to account changes
        this.appKit.subscribeAccount((account) => {
            if (account.isConnected && account.address !== this.currentAccount) {
                this.currentAccount = account.address;
                this.currentChainId = account.chainId;
                this.isConnected = true;

                // Store in localStorage
                localStorage.setItem('walletAddress', this.currentAccount);
                localStorage.setItem('walletConnected', 'true');

                console.log('✅ Wallet connected:', this.currentAccount);
                this.emit('connect', {
                    account: this.currentAccount,
                    chainId: this.currentChainId
                });

            } else if (!account.isConnected && this.isConnected) {
                this.handleDisconnection();
            }
        });
    }

    /**
     * Connect wallet (simplified)
     */
    async connect(walletType = 'auto') {
        try {
            console.log('🔄 Simplified wallet connection...');

            // This method is now handled directly in the HTML page
            // Just emit connecting state for compatibility
            this.emit('connecting');

            // The actual connection logic is in the HTML page
            console.log('✅ Connection initiated (handled by page)');

        } catch (error) {
            console.error('❌ Failed to connect wallet:', error);
            this.emit('error', error);
            throw error;
        }
    }

    /**
     * Handle disconnection
     */
    handleDisconnection() {
        console.log('🔄 Wallet disconnected');

        this.isConnected = false;
        this.currentAccount = null;
        this.currentChainId = null;
        this.provider = null;
        this.signer = null;

        // Clear localStorage
        localStorage.removeItem('walletAddress');
        localStorage.removeItem('walletConnected');

        this.emit('disconnect');
    }

    /**
     * Disconnect wallet
     */
    async disconnect() {
        try {
            if (!this.appKit) {
                this.handleDisconnection();
                return;
            }

            console.log('🔄 Disconnecting wallet...');

            // Disconnect using AppKit
            await this.appKit.disconnect();

            console.log('✅ Wallet disconnected');

        } catch (error) {
            console.error('❌ Failed to disconnect wallet:', error);
            this.emit('error', error);
            // Force disconnection even if AppKit fails
            this.handleDisconnection();
        }
    }

    /**
     * Switch network using AppKit
     */
    async switchNetwork(chainId) {
        try {
            if (!this.appKit) {
                throw new Error('AppKit not initialized');
            }

            console.log(`🔄 Switching to network ${chainId}...`);

            const network = window.NetworkUtils.getNetworkByChainId(chainId);
            if (!network) {
                throw new Error(`Unsupported network: ${chainId}`);
            }

            // Switch network using AppKit
            await this.appKit.switchNetwork(chainId);

            console.log(`✅ Switched to ${network.name}`);

            return network;

        } catch (error) {
            console.error('❌ Failed to switch network:', error);
            this.emit('error', error);
            throw error;
        }
    }

    /**
     * Get provider for contract interactions
     */
    getProvider() {
        if (!this.appKit) {
            throw new Error('AppKit not initialized');
        }

        return this.appKit.getWalletProvider();
    }

    /**
     * Get signer for transactions
     */
    async getSigner() {
        const provider = this.getProvider();
        if (!provider) {
            throw new Error('No provider available');
        }

        // Create ethers provider and signer
        const ethersProvider = new ethers.providers.Web3Provider(provider);
        return ethersProvider.getSigner();
    }

    /**
     * Get current wallet state
     */
    getState() {
        return {
            isConnected: this.isConnected,
            account: this.currentAccount,
            chainId: this.currentChainId,
            networkName: this.currentChainId ? 
                window.NetworkUtils.getNetworkName(this.currentChainId) : null
        };
    }

    /**
     * Event system
     */
    on(event, callback) {
        if (this.listeners[event]) {
            this.listeners[event].push(callback);
        }
    }

    off(event, callback) {
        if (this.listeners[event]) {
            const index = this.listeners[event].indexOf(callback);
            if (index > -1) {
                this.listeners[event].splice(index, 1);
            }
        }
    }

    emit(event, data) {
        if (this.listeners[event]) {
            this.listeners[event].forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`Error in ${event} listener:`, error);
                }
            });
        }
    }

    /**
     * Auto-reconnect on page load
     */
    async autoReconnect() {
        const wasConnected = localStorage.getItem('walletConnected') === 'true';
        const savedAddress = localStorage.getItem('walletAddress');

        if (wasConnected && savedAddress) {
            try {
                await this.connect('auto');
            } catch (error) {
                console.warn('Auto-reconnect failed:', error);
                // Clear invalid connection data
                localStorage.removeItem('walletConnected');
                localStorage.removeItem('walletAddress');
            }
        }
    }


}

// Create global wallet manager instance
window.walletManager = new WalletManager();

// Auto-reconnect when page loads (delayed to avoid conflicts)
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(() => window.walletManager.autoReconnect(), 500);
    });
} else {
    // DOM is already ready
    setTimeout(() => window.walletManager.autoReconnect(), 500);
}

console.log('✓ Wallet Manager loaded');
