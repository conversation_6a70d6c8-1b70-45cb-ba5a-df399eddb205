/**
 * Wallet Manager - Unified Wallet Connection and State Management
 * XMKF Digital Asset Platform
 */

class WalletManager {
    constructor() {
        this.appKit = null;
        this.isConnected = false;
        this.currentAccount = null;
        this.currentChainId = null;
        this.provider = null;
        this.signer = null;
        
        // Event listeners
        this.listeners = {
            connect: [],
            disconnect: [],
            accountChange: [],
            chainChange: [],
            error: []
        };

        this.init();
    }

    /**
     * Initialize AppKit
     */
    async init() {
        try {
            // Wait for AppKit config to be loaded
            if (!window.APPKIT_CONFIG) {
                throw new Error('AppKit configuration not loaded');
            }

            const { PROJECT_ID, NETWORKS, WALLET_CONFIG, THEME_CONFIG } = window.APPKIT_CONFIG;

            if (PROJECT_ID === 'YOUR_PROJECT_ID_HERE') {
                console.warn('⚠️ Please update PROJECT_ID in config.js');
                return;
            }

            // Initialize AppKit (this will be updated when we have the actual implementation)
            console.log('🔄 Initializing AppKit...');
            
            // For now, we'll prepare the configuration structure
            this.config = {
                projectId: PROJECT_ID,
                networks: Object.values(NETWORKS),
                wallets: WALLET_CONFIG,
                theme: THEME_CONFIG
            };

            console.log('✓ Wallet Manager initialized');
            
        } catch (error) {
            console.error('❌ Failed to initialize Wallet Manager:', error);
            this.emit('error', error);
        }
    }

    /**
     * Connect wallet
     */
    async connect(walletType = 'auto') {
        try {
            console.log(`🔄 Connecting to ${walletType} wallet...`);
            
            // This will be implemented with actual AppKit integration
            // For now, we'll simulate the connection process
            
            // Emit connecting state
            this.emit('connecting');
            
            // Simulate connection delay
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // Update connection state
            this.isConnected = true;
            this.currentAccount = '0x1234...5678'; // This will be the actual address
            this.currentChainId = 1; // This will be the actual chain ID
            
            // Store in localStorage for persistence
            localStorage.setItem('walletAddress', this.currentAccount);
            localStorage.setItem('walletConnected', 'true');
            
            console.log('✅ Wallet connected successfully');
            this.emit('connect', {
                account: this.currentAccount,
                chainId: this.currentChainId
            });
            
            return {
                account: this.currentAccount,
                chainId: this.currentChainId
            };
            
        } catch (error) {
            console.error('❌ Failed to connect wallet:', error);
            this.emit('error', error);
            throw error;
        }
    }

    /**
     * Disconnect wallet
     */
    async disconnect() {
        try {
            console.log('🔄 Disconnecting wallet...');
            
            // Clear connection state
            this.isConnected = false;
            this.currentAccount = null;
            this.currentChainId = null;
            this.provider = null;
            this.signer = null;
            
            // Clear localStorage
            localStorage.removeItem('walletAddress');
            localStorage.removeItem('walletConnected');
            
            console.log('✅ Wallet disconnected');
            this.emit('disconnect');
            
        } catch (error) {
            console.error('❌ Failed to disconnect wallet:', error);
            this.emit('error', error);
        }
    }

    /**
     * Switch network
     */
    async switchNetwork(chainId) {
        try {
            console.log(`🔄 Switching to network ${chainId}...`);
            
            const network = window.NetworkUtils.getNetworkByChainId(chainId);
            if (!network) {
                throw new Error(`Unsupported network: ${chainId}`);
            }
            
            // This will be implemented with actual AppKit integration
            this.currentChainId = chainId;
            
            console.log(`✅ Switched to ${network.name}`);
            this.emit('chainChange', chainId);
            
            return network;
            
        } catch (error) {
            console.error('❌ Failed to switch network:', error);
            this.emit('error', error);
            throw error;
        }
    }

    /**
     * Get current wallet state
     */
    getState() {
        return {
            isConnected: this.isConnected,
            account: this.currentAccount,
            chainId: this.currentChainId,
            networkName: this.currentChainId ? 
                window.NetworkUtils.getNetworkName(this.currentChainId) : null
        };
    }

    /**
     * Event system
     */
    on(event, callback) {
        if (this.listeners[event]) {
            this.listeners[event].push(callback);
        }
    }

    off(event, callback) {
        if (this.listeners[event]) {
            const index = this.listeners[event].indexOf(callback);
            if (index > -1) {
                this.listeners[event].splice(index, 1);
            }
        }
    }

    emit(event, data) {
        if (this.listeners[event]) {
            this.listeners[event].forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`Error in ${event} listener:`, error);
                }
            });
        }
    }

    /**
     * Auto-reconnect on page load
     */
    async autoReconnect() {
        const wasConnected = localStorage.getItem('walletConnected') === 'true';
        const savedAddress = localStorage.getItem('walletAddress');
        
        if (wasConnected && savedAddress) {
            try {
                await this.connect('auto');
            } catch (error) {
                console.warn('Auto-reconnect failed:', error);
                // Clear invalid connection data
                localStorage.removeItem('walletConnected');
                localStorage.removeItem('walletAddress');
            }
        }
    }
}

// Create global wallet manager instance
window.walletManager = new WalletManager();

// Auto-reconnect when page loads
document.addEventListener('DOMContentLoaded', () => {
    window.walletManager.autoReconnect();
});

console.log('✓ Wallet Manager loaded');
