/**
 * Wallet Manager - Unified Wallet Connection and State Management
 * XMKF Digital Asset Platform - Real AppKit Integration
 */

// Import AppKit modules (will be loaded via CDN)
let createAppKit, defaultConfig, mainnet, arbitrum, polygon, bsc;

class WalletManager {
    constructor() {
        this.appKit = null;
        this.isConnected = false;
        this.currentAccount = null;
        this.currentChainId = null;
        this.provider = null;
        this.signer = null;
        this.isInitialized = false;

        // Event listeners
        this.listeners = {
            connect: [],
            disconnect: [],
            accountChange: [],
            chainChange: [],
            error: [],
            connecting: []
        };

        // Wait for dependencies to load
        this.waitForDependencies().then(() => {
            this.init();
        });
    }

    /**
     * Wait for AppKit dependencies to load
     */
    async waitForDependencies() {
        return new Promise((resolve) => {
            const checkDependencies = () => {
                if (window.AppKit && window.APPKIT_CONFIG) {
                    // Extract AppKit modules from global scope
                    createAppKit = window.AppKit.createAppKit;
                    defaultConfig = window.AppKit.defaultConfig;
                    mainnet = window.AppKit.mainnet;
                    arbitrum = window.AppKit.arbitrum;
                    polygon = window.AppKit.polygon;
                    bsc = window.AppKit.bsc;
                    resolve();
                } else {
                    setTimeout(checkDependencies, 100);
                }
            };
            checkDependencies();
        });
    }

    /**
     * Initialize AppKit with real configuration
     */
    async init() {
        try {
            if (!window.APPKIT_CONFIG) {
                throw new Error('AppKit configuration not loaded');
            }

            const { PROJECT_ID, NETWORKS, WALLET_CONFIG, THEME_CONFIG } = window.APPKIT_CONFIG;

            console.log('🔄 Initializing AppKit with Project ID:', PROJECT_ID);

            // Define supported networks
            const networks = [
                mainnet,
                polygon,
                bsc,
                // Add Sepolia for testing
                {
                    chainId: 11155111,
                    name: 'Sepolia',
                    currency: 'ETH',
                    explorerUrl: 'https://sepolia.etherscan.io',
                    rpcUrl: 'https://sepolia.infura.io/v3/********************************'
                }
            ];

            // Create AppKit instance
            this.appKit = createAppKit({
                projectId: PROJECT_ID,
                networks: networks,
                defaultNetwork: mainnet,
                metadata: {
                    name: 'XMKF Digital Asset Platform',
                    description: 'Web3 Digital Asset Management Platform',
                    url: window.location.origin,
                    icons: ['/erc/images/header_icon.png']
                },
                themeMode: THEME_CONFIG.themeMode,
                themeVariables: THEME_CONFIG.themeVariables,
                enableAnalytics: true,
                enableOnramp: true
            });

            // Set up event listeners
            this.setupEventListeners();

            this.isInitialized = true;
            console.log('✅ AppKit initialized successfully');

        } catch (error) {
            console.error('❌ Failed to initialize AppKit:', error);
            this.emit('error', error);
        }
    }

    /**
     * Setup AppKit event listeners
     */
    setupEventListeners() {
        if (!this.appKit) return;

        // Listen to connection state changes
        this.appKit.subscribeState((state) => {
            const { open, selectedNetworkId } = state;

            if (selectedNetworkId !== this.currentChainId) {
                this.currentChainId = selectedNetworkId;
                this.emit('chainChange', selectedNetworkId);
            }
        });

        // Listen to account changes
        this.appKit.subscribeAccount((account) => {
            if (account.isConnected && account.address !== this.currentAccount) {
                this.currentAccount = account.address;
                this.currentChainId = account.chainId;
                this.isConnected = true;

                // Store in localStorage
                localStorage.setItem('walletAddress', this.currentAccount);
                localStorage.setItem('walletConnected', 'true');

                console.log('✅ Wallet connected:', this.currentAccount);
                this.emit('connect', {
                    account: this.currentAccount,
                    chainId: this.currentChainId
                });

            } else if (!account.isConnected && this.isConnected) {
                this.handleDisconnection();
            }
        });
    }

    /**
     * Connect wallet using AppKit
     */
    async connect(walletType = 'auto') {
        try {
            if (!this.isInitialized) {
                throw new Error('AppKit not initialized');
            }

            console.log(`🔄 Opening AppKit modal...`);

            // Emit connecting state
            this.emit('connecting');

            // Open AppKit modal
            this.appKit.open();

            // Return promise that resolves when connected
            return new Promise((resolve, reject) => {
                const timeout = setTimeout(() => {
                    reject(new Error('Connection timeout'));
                }, 30000); // 30 second timeout

                const onConnect = (data) => {
                    clearTimeout(timeout);
                    this.off('connect', onConnect);
                    this.off('error', onError);
                    resolve(data);
                };

                const onError = (error) => {
                    clearTimeout(timeout);
                    this.off('connect', onConnect);
                    this.off('error', onError);
                    reject(error);
                };

                this.on('connect', onConnect);
                this.on('error', onError);
            });

        } catch (error) {
            console.error('❌ Failed to connect wallet:', error);
            this.emit('error', error);
            throw error;
        }
    }

    /**
     * Handle disconnection
     */
    handleDisconnection() {
        console.log('🔄 Wallet disconnected');

        this.isConnected = false;
        this.currentAccount = null;
        this.currentChainId = null;
        this.provider = null;
        this.signer = null;

        // Clear localStorage
        localStorage.removeItem('walletAddress');
        localStorage.removeItem('walletConnected');

        this.emit('disconnect');
    }

    /**
     * Disconnect wallet
     */
    async disconnect() {
        try {
            if (!this.appKit) {
                this.handleDisconnection();
                return;
            }

            console.log('🔄 Disconnecting wallet...');

            // Disconnect using AppKit
            await this.appKit.disconnect();

            console.log('✅ Wallet disconnected');

        } catch (error) {
            console.error('❌ Failed to disconnect wallet:', error);
            this.emit('error', error);
            // Force disconnection even if AppKit fails
            this.handleDisconnection();
        }
    }

    /**
     * Switch network using AppKit
     */
    async switchNetwork(chainId) {
        try {
            if (!this.appKit) {
                throw new Error('AppKit not initialized');
            }

            console.log(`🔄 Switching to network ${chainId}...`);

            const network = window.NetworkUtils.getNetworkByChainId(chainId);
            if (!network) {
                throw new Error(`Unsupported network: ${chainId}`);
            }

            // Switch network using AppKit
            await this.appKit.switchNetwork(chainId);

            console.log(`✅ Switched to ${network.name}`);

            return network;

        } catch (error) {
            console.error('❌ Failed to switch network:', error);
            this.emit('error', error);
            throw error;
        }
    }

    /**
     * Get provider for contract interactions
     */
    getProvider() {
        if (!this.appKit) {
            throw new Error('AppKit not initialized');
        }

        return this.appKit.getWalletProvider();
    }

    /**
     * Get signer for transactions
     */
    async getSigner() {
        const provider = this.getProvider();
        if (!provider) {
            throw new Error('No provider available');
        }

        // Create ethers provider and signer
        const ethersProvider = new ethers.providers.Web3Provider(provider);
        return ethersProvider.getSigner();
    }

    /**
     * Get current wallet state
     */
    getState() {
        return {
            isConnected: this.isConnected,
            account: this.currentAccount,
            chainId: this.currentChainId,
            networkName: this.currentChainId ? 
                window.NetworkUtils.getNetworkName(this.currentChainId) : null
        };
    }

    /**
     * Event system
     */
    on(event, callback) {
        if (this.listeners[event]) {
            this.listeners[event].push(callback);
        }
    }

    off(event, callback) {
        if (this.listeners[event]) {
            const index = this.listeners[event].indexOf(callback);
            if (index > -1) {
                this.listeners[event].splice(index, 1);
            }
        }
    }

    emit(event, data) {
        if (this.listeners[event]) {
            this.listeners[event].forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`Error in ${event} listener:`, error);
                }
            });
        }
    }

    /**
     * Auto-reconnect on page load
     */
    async autoReconnect() {
        const wasConnected = localStorage.getItem('walletConnected') === 'true';
        const savedAddress = localStorage.getItem('walletAddress');
        
        if (wasConnected && savedAddress) {
            try {
                await this.connect('auto');
            } catch (error) {
                console.warn('Auto-reconnect failed:', error);
                // Clear invalid connection data
                localStorage.removeItem('walletConnected');
                localStorage.removeItem('walletAddress');
            }
        }
    }
}

// Create global wallet manager instance
window.walletManager = new WalletManager();

// Auto-reconnect when page loads
document.addEventListener('DOMContentLoaded', () => {
    window.walletManager.autoReconnect();
});

console.log('✓ Wallet Manager loaded');
