{"name": "@reown/appkit-adapter-ethers", "version": "1.7.11", "sideEffects": false, "type": "module", "main": "./dist/esm/src/index.js", "types": "./dist/types/src/index.d.ts", "files": ["dist", "!tsconfig.tsbuildinfo", "README.md"], "dependencies": {"@walletconnect/universal-provider": "2.21.3", "valtio": "1.13.2", "@reown/appkit": "1.7.11", "@reown/appkit-common": "1.7.11", "@reown/appkit-controllers": "1.7.11", "@reown/appkit-polyfills": "1.7.11", "@reown/appkit-scaffold-ui": "1.7.11", "@reown/appkit-utils": "1.7.11", "@reown/appkit-wallet": "1.7.11"}, "optionalDependencies": {"@coinbase/wallet-sdk": "4.3.0", "@safe-global/safe-apps-provider": "0.18.6", "@safe-global/safe-apps-sdk": "9.1.0"}, "peerDependencies": {"@ethersproject/sha2": "5.7.0", "ethers": ">=6"}, "devDependencies": {"@vitest/coverage-v8": "2.1.9", "@walletconnect/types": "2.21.3", "vitest": "2.1.9"}, "author": "Reown (https://discord.gg/reown)", "license": "Apache-2.0", "homepage": "https://github.com/reown-com/appkit", "repository": {"type": "git", "url": "git+https://github.com/reown-com/appkit.git"}, "bugs": {"url": "https://github.com/reown-com/appkit/issues"}, "keywords": ["appkit", "wallet", "onboarding", "reown", "dapps", "web3", "wagmi", "ethereum", "solana", "bitcoin"], "scripts": {"build:clean": "rm -rf dist", "build": "tsc --build tsconfig.build.json", "watch": "tsc --watch", "typecheck": "tsc --noEmit", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "test": "vitest run --coverage.enabled=true --coverage.reporter=json --coverage.reporter=json-summary --coverage.reportOnFailure=true"}}