<!-- 
    Unified Wallet Connection Button Component
    XMKF Digital Asset Platform - AppKit Integration
-->

<div id="wallet-button-container" class="wallet-button-container">
    <!-- Main Connect Button -->
    <button id="wallet-connect-btn" class="wallet-connect-btn" onclick="handleWalletConnect()">
        <img src="../erc/images/link_icon.svg" class="wallet-icon" alt="Wallet">
        <span id="wallet-btn-text">Connect wallet</span>
        <div id="wallet-loading" class="wallet-loading" style="display: none;">
            <div class="spinner"></div>
        </div>
    </button>

    <!-- Network Selector -->
    <div id="network-selector" class="network-selector" style="display: none;">
        <button id="current-network" class="network-btn" onclick="toggleNetworkDropdown()">
            <img id="network-icon" src="../erc/images/icon1.svg" class="network-icon" alt="Network">
            <span id="network-name">Ethereum</span>
            <img src="../erc/images/down.png" class="dropdown-arrow" alt="Dropdown">
        </button>
        
        <div id="network-dropdown" class="network-dropdown" style="display: none;">
            <div class="network-option" data-chain-id="1" onclick="switchNetwork(1)">
                <img src="../erc/images/icon1.svg" alt="Ethereum">
                <span>Ethereum</span>
            </div>
            <div class="network-option" data-chain-id="56" onclick="switchNetwork(56)">
                <img src="../erc/images/bnb.png" alt="BSC">
                <span>BSC</span>
            </div>
            <div class="network-option" data-chain-id="137" onclick="switchNetwork(137)">
                <img src="../erc/images/polygon.png" alt="Polygon">
                <span>Polygon</span>
            </div>
        </div>
    </div>

    <!-- Account Info (shown when connected) -->
    <div id="account-info" class="account-info" style="display: none;">
        <div class="account-address" id="account-address">0x1234...5678</div>
        <div class="account-balance" id="account-balance">0.000 ETH</div>
        <button class="disconnect-btn" onclick="handleWalletDisconnect()">Disconnect</button>
    </div>
</div>

<!-- Wallet Selection Modal -->
<div id="wallet-modal" class="wallet-modal" style="display: none;">
    <div class="wallet-modal-overlay" onclick="closeWalletModal()"></div>
    <div class="wallet-modal-content">
        <div class="wallet-modal-header">
            <h3>Connect Wallet</h3>
            <button class="close-btn" onclick="closeWalletModal()">&times;</button>
        </div>
        
        <div class="wallet-modal-body">
            <!-- Recommended Wallets -->
            <div class="wallet-section">
                <h4>Recommended</h4>
                <div class="wallet-grid">
                    <div class="wallet-option" data-wallet="metamask" onclick="connectWallet('metamask')">
                        <img src="../erc/images/metamask.png" alt="MetaMask">
                        <span>MetaMask</span>
                    </div>
                    <div class="wallet-option" data-wallet="walletConnect" onclick="connectWallet('walletConnect')">
                        <img src="../erc/images/walletconnect.png" alt="WalletConnect">
                        <span>WalletConnect</span>
                    </div>
                    <div class="wallet-option" data-wallet="coinbase" onclick="connectWallet('coinbase')">
                        <img src="../erc/images/coinbase.png" alt="Coinbase">
                        <span>Coinbase</span>
                    </div>
                </div>
            </div>

            <!-- Social Login -->
            <div class="wallet-section">
                <h4>Social Login</h4>
                <div class="wallet-grid">
                    <div class="wallet-option" data-wallet="google" onclick="connectWallet('google')">
                        <img src="../erc/images/google.png" alt="Google">
                        <span>Google</span>
                    </div>
                    <div class="wallet-option" data-wallet="discord" onclick="connectWallet('discord')">
                        <img src="../erc/images/discord.png" alt="Discord">
                        <span>Discord</span>
                    </div>
                </div>
            </div>

            <!-- Mobile Wallets -->
            <div class="wallet-section">
                <h4>Mobile Wallets</h4>
                <div class="wallet-grid">
                    <div class="wallet-option" data-wallet="trust" onclick="connectWallet('trust')">
                        <img src="../erc/images/trust.png" alt="Trust Wallet">
                        <span>Trust Wallet</span>
                    </div>
                    <div class="wallet-option" data-wallet="rainbow" onclick="connectWallet('rainbow')">
                        <img src="../erc/images/rainbow.png" alt="Rainbow">
                        <span>Rainbow</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="wallet-modal-footer">
            <p class="wallet-disclaimer">
                By connecting a wallet, you agree to our Terms of Service and Privacy Policy.
            </p>
        </div>
    </div>
</div>

<style>
/* Wallet Button Styles */
.wallet-button-container {
    display: flex;
    align-items: center;
    gap: 10px;
}

.wallet-connect-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    background: linear-gradient(135deg, #8e5729, #a66b3a);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.wallet-connect-btn:hover {
    background: linear-gradient(135deg, #a66b3a, #8e5729);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(142, 87, 41, 0.3);
}

.wallet-connect-btn.connected {
    background: linear-gradient(135deg, #28a745, #20c997);
}

.wallet-icon {
    width: 20px;
    height: 20px;
}

.wallet-loading {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
}

.spinner {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Network Selector */
.network-selector {
    position: relative;
}

.network-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: rgba(142, 87, 41, 0.1);
    border: 1px solid rgba(142, 87, 41, 0.3);
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.3s ease;
}

.network-btn:hover {
    background: rgba(142, 87, 41, 0.2);
}

.network-icon {
    width: 16px;
    height: 16px;
}

.dropdown-arrow {
    width: 12px;
    height: 12px;
    transition: transform 0.3s ease;
}

.network-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    margin-top: 4px;
}

.network-option {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px;
    cursor: pointer;
    transition: background 0.2s ease;
}

.network-option:hover {
    background: #f8f9fa;
}

.network-option img {
    width: 20px;
    height: 20px;
}

/* Account Info */
.account-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
    padding: 8px 12px;
    background: rgba(40, 167, 69, 0.1);
    border: 1px solid rgba(40, 167, 69, 0.3);
    border-radius: 6px;
    font-size: 12px;
}

.account-address {
    font-weight: 600;
    color: #28a745;
}

.account-balance {
    color: #666;
}

.disconnect-btn {
    margin-top: 4px;
    padding: 4px 8px;
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 10px;
}

/* Wallet Modal */
.wallet-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.wallet-modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
}

.wallet-modal-content {
    position: relative;
    background: white;
    border-radius: 12px;
    width: 90%;
    max-width: 480px;
    max-height: 80vh;
    overflow-y: auto;
}

.wallet-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #eee;
}

.wallet-modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.close-btn {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #999;
}

.wallet-modal-body {
    padding: 20px;
}

.wallet-section {
    margin-bottom: 24px;
}

.wallet-section h4 {
    margin: 0 0 12px 0;
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

.wallet-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 12px;
}

.wallet-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 16px;
    border: 1px solid #eee;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.wallet-option:hover {
    border-color: #8e5729;
    background: rgba(142, 87, 41, 0.05);
}

.wallet-option img {
    width: 32px;
    height: 32px;
}

.wallet-option span {
    font-size: 12px;
    font-weight: 500;
    text-align: center;
}

.wallet-modal-footer {
    padding: 20px;
    border-top: 1px solid #eee;
    text-align: center;
}

.wallet-disclaimer {
    margin: 0;
    font-size: 12px;
    color: #666;
    line-height: 1.4;
}

/* Responsive Design */
@media (max-width: 768px) {
    .wallet-button-container {
        flex-direction: column;
        align-items: stretch;
    }
    
    .wallet-modal-content {
        width: 95%;
        margin: 20px;
    }
    
    .wallet-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}
</style>

<script>
// Wallet Button Component JavaScript
function handleWalletConnect() {
    if (window.walletManager && window.walletManager.isConnected) {
        // Already connected, show account info
        toggleAccountInfo();
    } else {
        // Show wallet selection modal
        showWalletModal();
    }
}

function showWalletModal() {
    document.getElementById('wallet-modal').style.display = 'flex';
    document.body.style.overflow = 'hidden';
}

function closeWalletModal() {
    document.getElementById('wallet-modal').style.display = 'none';
    document.body.style.overflow = 'auto';
}

async function connectWallet(walletType) {
    try {
        // Show loading state
        showWalletLoading();
        
        // Close modal
        closeWalletModal();
        
        // Connect using wallet manager
        if (window.walletManager) {
            await window.walletManager.connect(walletType);
        } else {
            throw new Error('Wallet manager not initialized');
        }
        
    } catch (error) {
        console.error('Wallet connection failed:', error);
        hideWalletLoading();
        
        if (window.layer) {
            layer.msg('Connection failed: ' + error.message, {icon: 2});
        } else {
            alert('Connection failed: ' + error.message);
        }
    }
}

async function handleWalletDisconnect() {
    try {
        if (window.walletManager) {
            await window.walletManager.disconnect();
        }
    } catch (error) {
        console.error('Disconnect failed:', error);
    }
}

function showWalletLoading() {
    document.getElementById('wallet-loading').style.display = 'block';
    document.getElementById('wallet-btn-text').textContent = 'Connecting...';
}

function hideWalletLoading() {
    document.getElementById('wallet-loading').style.display = 'none';
    document.getElementById('wallet-btn-text').textContent = 'Connect wallet';
}

function toggleNetworkDropdown() {
    const dropdown = document.getElementById('network-dropdown');
    const arrow = document.querySelector('.dropdown-arrow');
    
    if (dropdown.style.display === 'none' || !dropdown.style.display) {
        dropdown.style.display = 'block';
        arrow.style.transform = 'rotate(180deg)';
    } else {
        dropdown.style.display = 'none';
        arrow.style.transform = 'rotate(0deg)';
    }
}

async function switchNetwork(chainId) {
    try {
        if (window.walletManager) {
            await window.walletManager.switchNetwork(chainId);
        }
        
        // Close dropdown
        document.getElementById('network-dropdown').style.display = 'none';
        document.querySelector('.dropdown-arrow').style.transform = 'rotate(0deg)';
        
    } catch (error) {
        console.error('Network switch failed:', error);
        if (window.layer) {
            layer.msg('Network switch failed: ' + error.message, {icon: 2});
        }
    }
}

function toggleAccountInfo() {
    const accountInfo = document.getElementById('account-info');
    if (accountInfo.style.display === 'none' || !accountInfo.style.display) {
        accountInfo.style.display = 'block';
    } else {
        accountInfo.style.display = 'none';
    }
}

// Listen to wallet manager events
if (window.walletManager) {
    window.walletManager.on('connect', (data) => {
        hideWalletLoading();
        document.getElementById('wallet-btn-text').textContent = 'Connected';
        document.getElementById('wallet-connect-btn').classList.add('connected');
        document.getElementById('network-selector').style.display = 'block';
        
        // Update account info
        document.getElementById('account-address').textContent = 
            data.account.substring(0, 6) + '...' + data.account.substring(data.account.length - 4);
    });
    
    window.walletManager.on('disconnect', () => {
        hideWalletLoading();
        document.getElementById('wallet-btn-text').textContent = 'Connect wallet';
        document.getElementById('wallet-connect-btn').classList.remove('connected');
        document.getElementById('network-selector').style.display = 'none';
        document.getElementById('account-info').style.display = 'none';
    });
    
    window.walletManager.on('chainChange', (chainId) => {
        const networkName = window.NetworkUtils ? 
            window.NetworkUtils.getNetworkName(chainId) : 'Unknown';
        document.getElementById('network-name').textContent = networkName;
    });
}

// Close dropdown when clicking outside
document.addEventListener('click', (event) => {
    const networkSelector = document.getElementById('network-selector');
    if (networkSelector && !networkSelector.contains(event.target)) {
        document.getElementById('network-dropdown').style.display = 'none';
        document.querySelector('.dropdown-arrow').style.transform = 'rotate(0deg)';
    }
});

console.log('✓ Wallet Button Component loaded');
</script>
