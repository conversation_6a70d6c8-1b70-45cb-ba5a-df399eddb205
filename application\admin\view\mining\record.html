{extend name="public:template" /}

{block name="content"}
<h2 class="section-title">质押购买列表</h2>
<div style="text-align: center;font-size: 23px;color: red"></div>

 <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="card-header-form ml-auto" style="margin: 0!important;">
                        <form action="">
                            <div style="display: flex">
                                <div class="col-4">
                                    <input type="text" class="form-control" name="address"  placeholder="用户地址" value="{$params.address}">
                                </div>

                                <div class="col-4">
                                    <select class="col-4" style="background-color: rgba(0,0,0,0.5);color: #fff;" name="type">
                                        <option value="0">类型</option>
                                        <option value="1" {if condition="$params.type eq 1"} selected{/if}>收益余额</option>
                                        <option value="2" {if condition="$params.type eq 2"} selected{/if}>钱包余额</option>
                                    </select>
                                </div>

                                <div class="col-3">
                                    <button  class="btn btn-icon icon-left btn-primary">搜索</button >
                                </div>
                            </div>

                        </form>
                    </div>
                </div>

                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-md">
                            <tr>
                                <th>质押编号</th>
                                <th>用户地址</th>
                                <th>类型</th>
                                <th>质押金额</th>
                                <th>矿机ID</th>
                                <th>矿机名称</th>
                                <th>质押天数</th>
                                <th>日化收益（%）</th>
                                <th>买入状态</th>
                                <th>购买时间</th>
                                <th>结束时间</th>
<!--                                <th class="text-center">操作</th>-->
                            </tr>
                            {volist name="data" id="vo" key="key"}
                            <tr>
                                <td>{$vo.id}</td>
                                <td>{$vo.fish->address}</td>
                                <td>{$vo.type_text}</td>
                                <td>{$vo.freeze_money}</td>
                                <td>{$vo.mining->id}</td>
                                <td>{$vo.mining->name}</td>
                                <td>{$vo.mining->freeze}</td>
                                <td>{$vo.mining->profit}</td>
                                <td>{$vo.buy_status_text}</td>
                                <td>{:date('Y-m-d H:i:s',$vo.buy_time)}</td>
                                <td>{:date('Y-m-d H:i:s',$vo.end_time)}</td>
                                <td>
                                    {if condition="$vo.buy_status eq 0"}
                                    <a href="{:url('Mining/record_detail', ['id' => $vo.id])}" class="btn btn-icon icon-left btn-primary mr-3"><i class="far fa-edit"></i> 审核</a>
                                    {/if}
                                </td>
                            </tr>
                            {/volist}
                        </table>
                    </div>
                </div>

                <div class="card-footer text-right">
                    <nav class="d-inline-block">
                        {$data|raw}
                    </nav>
                </div>
            </div>
        </div>
    </div>
{/block}


{block name="extend-script"}
<script type="text/javascript">

    function delImage(id) {
        swal({
            title: '确定删除该数据？',
            icon: 'warning',
            buttons: ['取消', '确认'],
            dangerMode: true,
        })
            .then((willDelete) => {
                if (! willDelete)
                    return;

                $.ajax({
                    url: "{:url('Mining/destroy_record')}",
                    method: 'post',
                    data: {id: id},
                    dataType: 'json',
                    success(res) {
                        if (res.success === true) {
                            swal('操作成功', {buttons: false, icon: 'success'});
                            setTimeout(function () { location.reload() }, 1500)
                        }
                        if (res.success === false) swal('出现错误', res.err_msg, 'error');
                    }
                })
            });
    }
</script>
{/block}
