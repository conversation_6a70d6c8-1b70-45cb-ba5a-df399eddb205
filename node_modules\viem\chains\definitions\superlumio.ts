import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js'

export const superlumio = /*#__PURE__*/ defineChain({
  id: 8866,
  name: 'SuperLumio',
  nativeCurrency: { name: '<PERSON><PERSON>', symbol: 'ETH', decimals: 18 },
  rpcUrls: {
    default: {
      http: ['https://mainnet.lumio.io'],
    },
  },
  blockExplorers: {
    default: {
      name: 'Lumio explorer',
      url: 'https://explorer.lumio.io',
    },
  },
  testnet: false,
})
