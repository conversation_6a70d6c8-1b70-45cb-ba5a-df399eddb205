{"version": 3, "file": "fallback.js", "sourceRoot": "", "sources": ["../../../clients/transports/fallback.ts"], "names": [], "mappings": ";;AAyGA,4BAqGC;AAED,kCAWC;AAGD,wCA4FC;AA1TD,kDAA6D;AAC7D,gDAG4B;AAG5B,iDAA0C;AAE1C,6DAK6B;AA2F7B,SAAgB,QAAQ,CACtB,WAAuB,EACvB,SAAkC,EAAE;IAEpC,MAAM,EACJ,GAAG,GAAG,UAAU,EAChB,IAAI,GAAG,UAAU,EACjB,IAAI,GAAG,KAAK,EACZ,WAAW,EAAE,YAAY,GAAG,WAAW,EACvC,UAAU,EACV,UAAU,GACX,GAAG,MAAM,CAAA;IACV,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,eAAe,GAAG,KAAK,EAAE,OAAO,EAAE,GAAG,IAAI,EAAE,EAAE,EAAE;QAC/D,IAAI,UAAU,GAAG,WAAW,CAAA;QAE5B,IAAI,UAAU,GAAiB,GAAG,EAAE,GAAE,CAAC,CAAA;QAEvC,MAAM,SAAS,GAAG,IAAA,oCAAe,EAC/B;YACE,GAAG;YACH,IAAI;YACJ,KAAK,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE;gBAC9B,IAAI,QAA6B,CAAA;gBAEjC,MAAM,KAAK,GAAG,KAAK,EAAE,CAAC,GAAG,CAAC,EAAgB,EAAE;oBAC1C,MAAM,SAAS,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;wBAC9B,GAAG,IAAI;wBACP,KAAK;wBACL,UAAU,EAAE,CAAC;wBACb,OAAO;qBACR,CAAC,CAAA;oBACF,IAAI,CAAC;wBACH,MAAM,QAAQ,GAAG,MAAM,SAAS,CAAC,OAAO,CAAC;4BACvC,MAAM;4BACN,MAAM;yBACA,CAAC,CAAA;wBAET,UAAU,CAAC;4BACT,MAAM;4BACN,MAAM,EAAE,MAAmB;4BAC3B,QAAQ;4BACR,SAAS;4BACT,MAAM,EAAE,SAAS;yBAClB,CAAC,CAAA;wBAEF,OAAO,QAAQ,CAAA;oBACjB,CAAC;oBAAC,OAAO,GAAG,EAAE,CAAC;wBACb,UAAU,CAAC;4BACT,KAAK,EAAE,GAAY;4BACnB,MAAM;4BACN,MAAM,EAAE,MAAmB;4BAC3B,SAAS;4BACT,MAAM,EAAE,OAAO;yBAChB,CAAC,CAAA;wBAEF,IAAI,YAAY,CAAC,GAAY,CAAC;4BAAE,MAAM,GAAG,CAAA;wBAGzC,IAAI,CAAC,KAAK,UAAU,CAAC,MAAM,GAAG,CAAC;4BAAE,MAAM,GAAG,CAAA;wBAG1C,QAAQ,KAAK,UAAU,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,EAAE;4BACtD,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GACxB,SAAS,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,IAAI,EAAE,CAAA;4BAC3C,IAAI,OAAO;gCAAE,OAAO,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;4BAC5C,IAAI,OAAO;gCAAE,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;4BAC7C,OAAO,IAAI,CAAA;wBACb,CAAC,CAAC,CAAA;wBACF,IAAI,CAAC,QAAQ;4BAAE,MAAM,GAAG,CAAA;wBAGxB,OAAO,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAA;oBACrB,CAAC;gBACH,CAAC,CAAA;gBACD,OAAO,KAAK,EAAE,CAAA;YAChB,CAAC;YACD,UAAU;YACV,UAAU;YACV,IAAI,EAAE,UAAU;SACjB,EACD;YACE,UAAU,EAAE,CAAC,EAAgB,EAAE,EAAE,CAAC,CAAC,UAAU,GAAG,EAAE,CAAC;YACnD,UAAU,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC;SACjE,CACF,CAAA;QAED,IAAI,IAAI,EAAE,CAAC;YACT,MAAM,WAAW,GAAG,CAAC,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAgB,CAAA;YACzE,cAAc,CAAC;gBACb,KAAK;gBACL,QAAQ,EAAE,WAAW,CAAC,QAAQ,IAAI,eAAe;gBACjD,YAAY,EAAE,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,UAAU,GAAG,WAAyB,CAAC;gBACvE,IAAI,EAAE,WAAW,CAAC,IAAI;gBACtB,WAAW,EAAE,WAAW,CAAC,WAAW;gBACpC,OAAO,EAAE,WAAW,CAAC,OAAO;gBAC5B,UAAU;gBACV,OAAO,EAAE,WAAW,CAAC,OAAO;aAC7B,CAAC,CAAA;QACJ,CAAC;QACD,OAAO,SAAS,CAAA;IAClB,CAAC,CAAkC,CAAA;AACrC,CAAC;AAED,SAAgB,WAAW,CAAC,KAAY;IACtC,IAAI,MAAM,IAAI,KAAK,IAAI,OAAO,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;QACtD,IACE,KAAK,CAAC,IAAI,KAAK,oCAA2B,CAAC,IAAI;YAC/C,KAAK,CAAC,IAAI,KAAK,iCAAwB,CAAC,IAAI;YAC5C,gCAAsB,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;YACtD,KAAK,CAAC,IAAI,KAAK,IAAI;YAEnB,OAAO,IAAI,CAAA;IACf,CAAC;IACD,OAAO,KAAK,CAAA;AACd,CAAC;AAGD,SAAgB,cAAc,CAAC,EAC7B,KAAK,EACL,QAAQ,GAAG,KAAK,EAChB,YAAY,EACZ,IAAI,EACJ,WAAW,GAAG,EAAE,EAChB,OAAO,GAAG,KAAK,EACf,UAAU,EACV,OAAO,GAAG,EAAE,GAUb;IACC,MAAM,EAAE,SAAS,EAAE,eAAe,GAAG,GAAG,EAAE,OAAO,EAAE,aAAa,GAAG,GAAG,EAAE,GACtE,OAAO,CAAA;IAIT,MAAM,OAAO,GAAa,EAAE,CAAA;IAE5B,MAAM,eAAe,GAAG,KAAK,IAAI,EAAE;QAEjC,MAAM,MAAM,GAAW,MAAM,OAAO,CAAC,GAAG,CACtC,UAAU,CAAC,GAAG,CAAC,KAAK,EAAE,SAAS,EAAE,EAAE;YACjC,MAAM,UAAU,GAAG,SAAS,CAAC,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,CAAA;YAE/D,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;YACxB,IAAI,GAAW,CAAA;YACf,IAAI,OAAe,CAAA;YACnB,IAAI,CAAC;gBACH,MAAM,CAAC,IAAI;oBACT,CAAC,CAAC,IAAI,CAAC,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC;oBACjC,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,eAAe,EAAE,CAAC,CAAC,CAAA;gBACpD,OAAO,GAAG,CAAC,CAAA;YACb,CAAC;YAAC,MAAM,CAAC;gBACP,OAAO,GAAG,CAAC,CAAA;YACb,CAAC;oBAAS,CAAC;gBACT,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;YAClB,CAAC;YACD,MAAM,OAAO,GAAG,GAAG,GAAG,KAAK,CAAA;YAC3B,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,CAAA;QAC7B,CAAC,CAAC,CACH,CAAA;QAID,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QACpB,IAAI,OAAO,CAAC,MAAM,GAAG,WAAW;YAAE,OAAO,CAAC,KAAK,EAAE,CAAA;QAGjD,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CACzB,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CACxB,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC,OAAO,CAAC,CAAC,CAClD,CACF,CAAA;QAGD,MAAM,MAAM,GAAG,UAAU;aACtB,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YACZ,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAA;YAC5D,MAAM,WAAW,GACf,SAAS,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,OAAO,EAAE,EAAE,CAAC,GAAG,GAAG,OAAO,EAAE,CAAC,CAAC;gBACpD,SAAS,CAAC,MAAM,CAAA;YAClB,MAAM,YAAY,GAAG,CAAC,GAAG,WAAW,GAAG,UAAU,CAAA;YAEjD,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAA;YAC5D,MAAM,cAAc,GAClB,SAAS,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,OAAO,EAAE,EAAE,CAAC,GAAG,GAAG,OAAO,EAAE,CAAC,CAAC;gBACpD,SAAS,CAAC,MAAM,CAAA;YAElB,IAAI,cAAc,KAAK,CAAC;gBAAE,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;YACvC,OAAO;gBACL,aAAa,GAAG,YAAY,GAAG,eAAe,GAAG,cAAc;gBAC/D,CAAC;aACF,CAAA;QACH,CAAC,CAAC;aACD,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QAG9B,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QAGlD,MAAM,IAAA,cAAI,EAAC,QAAQ,CAAC,CAAA;QACpB,eAAe,EAAE,CAAA;IACnB,CAAC,CAAA;IACD,eAAe,EAAE,CAAA;AACnB,CAAC"}