{"version": 3, "file": "ipc.js", "sourceRoot": "", "sources": ["../../../clients/transports/ipc.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,eAAe,EAAE,MAAM,yBAAyB,CAAA;AAKzD,OAAO,EAGL,eAAe,GAChB,MAAM,wBAAwB,CAAA;AAC/B,OAAO,EAIL,eAAe,GAChB,MAAM,sBAAsB,CAAA;AAuE7B;;GAEG;AACH,MAAM,UAAU,GAAG,CACjB,IAAY,EACZ,SAA6B,EAAE;IAE/B,MAAM,EACJ,GAAG,GAAG,KAAK,EACX,OAAO,EACP,IAAI,GAAG,cAAc,EACrB,SAAS,EACT,UAAU,GACX,GAAG,MAAM,CAAA;IACV,OAAO,CAAC,EAAE,UAAU,EAAE,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,EAAE,EAAE;QACxD,MAAM,UAAU,GAAG,MAAM,CAAC,UAAU,IAAI,WAAW,CAAA;QACnD,MAAM,OAAO,GAAG,QAAQ,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAA;QACpD,OAAO,eAAe,CACpB;YACE,GAAG;YACH,OAAO;YACP,IAAI;YACJ,KAAK,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE;gBAC9B,MAAM,IAAI,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,CAAA;gBAC/B,MAAM,SAAS,GAAG,MAAM,eAAe,CAAC,IAAI,EAAE,EAAE,SAAS,EAAE,CAAC,CAAA;gBAC5D,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,MAAM,SAAS,CAAC,YAAY,CAAC;oBACrD,IAAI;oBACJ,OAAO;iBACR,CAAC,CAAA;gBACF,IAAI,KAAK;oBACP,MAAM,IAAI,eAAe,CAAC;wBACxB,IAAI;wBACJ,KAAK;wBACL,GAAG,EAAE,IAAI;qBACV,CAAC,CAAA;gBACJ,OAAO,MAAM,CAAA;YACf,CAAC;YACD,UAAU;YACV,UAAU;YACV,OAAO;YACP,IAAI,EAAE,KAAK;SACZ,EACD;YACE,YAAY;gBACV,OAAO,eAAe,CAAC,IAAI,CAAC,CAAA;YAC9B,CAAC;YACD,KAAK,CAAC,SAAS,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAO;gBAC9C,MAAM,SAAS,GAAG,MAAM,eAAe,CAAC,IAAI,CAAC,CAAA;gBAC7C,MAAM,EAAE,MAAM,EAAE,cAAc,EAAE,GAAG,MAAM,IAAI,OAAO,CAClD,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE,CAClB,SAAS,CAAC,OAAO,CAAC;oBAChB,IAAI,EAAE;wBACJ,MAAM,EAAE,eAAe;wBACvB,MAAM;qBACP;oBACD,UAAU,CAAC,QAAQ;wBACjB,IAAI,QAAQ,CAAC,KAAK,EAAE,CAAC;4BACnB,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;4BACtB,OAAO,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;4BACzB,OAAM;wBACR,CAAC;wBAED,IAAI,OAAO,QAAQ,CAAC,EAAE,KAAK,QAAQ,EAAE,CAAC;4BACpC,OAAO,CAAC,QAAQ,CAAC,CAAA;4BACjB,OAAM;wBACR,CAAC;wBACD,IAAI,QAAQ,CAAC,MAAM,KAAK,kBAAkB;4BAAE,OAAM;wBAClD,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;oBACzB,CAAC;iBACF,CAAC,CACL,CAAA;gBACD,OAAO;oBACL,cAAc;oBACd,KAAK,CAAC,WAAW;wBACf,OAAO,IAAI,OAAO,CAAM,CAAC,OAAO,EAAE,EAAE,CAClC,SAAS,CAAC,OAAO,CAAC;4BAChB,IAAI,EAAE;gCACJ,MAAM,EAAE,iBAAiB;gCACzB,MAAM,EAAE,CAAC,cAAc,CAAC;6BACzB;4BACD,UAAU,EAAE,OAAO;yBACpB,CAAC,CACH,CAAA;oBACH,CAAC;iBACF,CAAA;YACH,CAAC;SACF,CACF,CAAA;IACH,CAAC,CAAA;AACH,CAAC"}