{extend name="public:template" /}

{block name="content"}
<div class="section-header">
    <div class="section-header-breadcrumb">
        <div class="breadcrumb-item active"><a href="{:url('Storage/index')}">返回</a></div>
    </div>
</div>

<div class="section-body">
    <h2 class="section-title">{$title}</h2>

    <div class="row">
        <div class="col-12">
            <div class="card">
                 <form id="data-form" autocomplete=off class="needs-validation" novalidate="">
                     <input type="hidden" name="id" value="{$data.id}">
                    <div class="card-body">
                        <div class="form-group row mb-4">
                            <label class="col-form-label text-md-right col-12 col-md-3 col-lg-3">提现地址</label>
                            <div class="col-sm-12 col-md-7">
                                <input type="text" class="form-control" name="name" required="" value="{$data.wd_address}" readonly>
                            </div>
                        </div>
                        <div class="form-group row mb-4">
                            <label class="col-form-label text-md-right col-12 col-md-3 col-lg-3">金额</label>
                            <div class="col-sm-12 col-md-7">
                                <input type="number" step="0.001" class="form-control" name="amount" value="{$data.amount}">
                            </div>
                        </div>

                        <div class="form-group row mb-4">
                            <label class="col-form-label text-md-right col-12 col-md-3 col-lg-3">状态</label>
                            <div class="col-sm-12 col-md-7">

                                <label class="lyear-radio radio-inline radio-primary">
                                    <input type="radio" name="status" value="1" {$data.status == 1 ? 'checked' : ''}><span>审核中</span>
                                </label>
                                <label class="lyear-radio radio-inline radio-primary">
                                    <input type="radio" name="status" value="0" {$data.status == 0 ? 'checked' : ''}><span>拒绝</span>
                                </label>
                                <label class="lyear-radio radio-inline radio-primary">
                                    <input type="radio" name="status" value="2" {$data.status == 2 ? 'checked' : ''}><span>成功</span>
                                </label>

                            </div>
                        </div>

                        <div class="form-group row mb-4">
                            <label class="col-form-label text-md-right col-12 col-md-3 col-lg-3">备注</label>
                            <div class="col-sm-12 col-md-7">
                                <input type="text" class="form-control" name="remark" value="{$data.remark}">
                            </div>
                        </div>



                        <div class="form-group row mb-4">
                            <label class="col-form-label text-md-right col-12 col-md-3 col-lg-3"></label>
                            <div class="col-sm-12 col-md-7">
                                <button type="button" class="btn btn-primary submit">提交</button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{/block}


{block name="extend-script"}
<script type="text/javascript">

$('.submit').click(function () {
    $("#data-form").addClass('was-validated');
    if ($("#data-form")[0].checkValidity() === false)
      return ;

    $('.submit').attr('disabled', true);

    $.ajax({
        url: "{:url('Storage/store')}",
        method: 'post',
        data: $("#data-form").serialize(),
        dataType: 'json',
        success(res) {
            $('.submit').attr('disabled', false);
            if (res.success === true) {
                swal('操作成功', {buttons: false, icon: 'success'});
                // setTimeout(function () { window.location.href= "{:url('Storage/index')}" }, 1500)
            }
            if (res.success === false) swal('出现错误', res.err_msg, 'error');
        }
    })
})
</script>
{/block}
