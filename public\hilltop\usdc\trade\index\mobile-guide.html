<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>移动端钱包连接指南 - XMKF Digital Asset Platform</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }

        .header h1 {
            font-size: 1.8rem;
            margin-bottom: 8px;
        }

        .header p {
            font-size: 1rem;
            opacity: 0.9;
        }

        .content {
            padding: 30px 20px;
        }

        .back-btn {
            display: inline-flex;
            align-items: center;
            padding: 10px 20px;
            background: #28a745;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 500;
            margin-bottom: 20px;
            font-size: 14px;
        }

        .back-btn:hover {
            background: #218838;
        }

        .step-card {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #007bff;
        }

        .step-card h3 {
            color: #007bff;
            margin-bottom: 12px;
            font-size: 1.2rem;
        }

        .step-card p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 10px;
        }

        .step-card ul {
            margin-left: 20px;
            color: #666;
        }

        .step-card li {
            margin-bottom: 8px;
            line-height: 1.5;
        }

        .wallet-method {
            background: white;
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .wallet-method h4 {
            color: #1a1a1a;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            font-size: 1.1rem;
        }

        .wallet-method .icon {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            color: white;
        }

        .metamask { background: linear-gradient(135deg, #f6851b, #e2761b); }
        .trustwallet { background: linear-gradient(135deg, #3375bb, #1e5a96); }
        .coinbase { background: linear-gradient(135deg, #0052ff, #0041cc); }

        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 16px;
            margin: 20px 0;
        }

        .warning h4 {
            color: #856404;
            margin-bottom: 8px;
        }

        .warning p {
            color: #856404;
            margin: 0;
            line-height: 1.5;
        }

        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 16px;
            margin: 20px 0;
        }

        .success h4 {
            color: #155724;
            margin-bottom: 8px;
        }

        .success p {
            color: #155724;
            margin: 0;
            line-height: 1.5;
        }

        .test-btn {
            display: block;
            width: 100%;
            padding: 15px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            text-align: center;
            font-weight: 500;
            margin: 20px 0;
            font-size: 16px;
        }

        .test-btn:hover {
            background: #0056b3;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 15px;
            }
            
            .header {
                padding: 20px 15px;
            }
            
            .content {
                padding: 20px 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📱 移动端钱包连接指南</h1>
            <p>在手机上安全连接您的Web3钱包</p>
        </div>

        <div class="content">
            <a href="usdc-appkit.html" class="back-btn">
                ← 返回交易平台
            </a>

            <div class="warning">
                <h4>⚠️ 重要提醒</h4>
                <p>移动端钱包连接与桌面端不同。请按照以下步骤操作以确保安全连接。</p>
            </div>

            <div class="step-card">
                <h3>🔍 第一步：检查钱包安装</h3>
                <p>确保您的手机已安装以下任一钱包应用：</p>
                <div class="wallet-method">
                    <h4><span class="icon metamask">🦊</span>MetaMask</h4>
                    <p>从App Store或Google Play下载官方MetaMask应用</p>
                </div>
                <div class="wallet-method">
                    <h4><span class="icon trustwallet">🛡️</span>Trust Wallet</h4>
                    <p>从App Store或Google Play下载官方Trust Wallet应用</p>
                </div>
                <div class="wallet-method">
                    <h4><span class="icon coinbase">🔵</span>Coinbase Wallet</h4>
                    <p>从App Store或Google Play下载官方Coinbase Wallet应用</p>
                </div>
            </div>

            <div class="step-card">
                <h3>🌐 第二步：选择连接方式</h3>
                <p>移动端有两种主要连接方式：</p>
                <ul>
                    <li><strong>方式一（推荐）：</strong>在钱包应用内置浏览器中打开网站</li>
                    <li><strong>方式二：</strong>使用深度链接从外部浏览器连接</li>
                </ul>
            </div>

            <div class="step-card">
                <h3>🔗 方式一：钱包内置浏览器（推荐）</h3>
                <p>这是最安全和可靠的连接方式：</p>
                <ul>
                    <li>打开您的钱包应用（MetaMask、Trust Wallet等）</li>
                    <li>找到"浏览器"或"DApp"功能</li>
                    <li>在地址栏输入：<code>localhost:8000/hilltop/usdc/trade/index/usdc-appkit.html</code></li>
                    <li>网站会自动检测到钱包环境</li>
                    <li>点击"Connect Wallet"即可连接</li>
                </ul>
            </div>

            <div class="step-card">
                <h3>📲 方式二：深度链接连接</h3>
                <p>从手机浏览器连接到钱包应用：</p>
                <ul>
                    <li>在手机浏览器中打开网站</li>
                    <li>点击"Connect Wallet"选择钱包</li>
                    <li>系统会尝试打开对应的钱包应用</li>
                    <li>在钱包中确认连接</li>
                    <li>返回浏览器查看连接状态</li>
                </ul>
            </div>

            <div class="success">
                <h4>✅ 连接成功标志</h4>
                <p>连接成功后，您会看到钱包地址显示在页面上，并且可以进行交易操作。</p>
            </div>

            <a href="usdc-appkit.html" class="test-btn">
                🚀 立即测试连接
            </a>

            <div class="step-card">
                <h3>🛠️ 故障排除</h3>
                <p>如果遇到连接问题，请尝试以下解决方案：</p>
                <ul>
                    <li><strong>显示"未安装"：</strong>尝试在钱包应用内置浏览器中打开</li>
                    <li><strong>连接超时：</strong>检查网络连接，重新尝试</li>
                    <li><strong>无法打开钱包：</strong>确认钱包应用已正确安装</li>
                    <li><strong>连接失败：</strong>清除浏览器缓存后重试</li>
                </ul>
            </div>

            <div class="warning">
                <h4>🔒 安全提醒</h4>
                <p>• 只在官方钱包应用中进行连接<br>
                • 不要在不安全的网络环境中连接钱包<br>
                • 定期检查连接的DApp权限<br>
                • 遇到可疑情况立即断开连接</p>
            </div>
        </div>
    </div>

    <script>
        // 检测设备类型并显示相应提示
        function detectDevice() {
            const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
            
            if (!isMobile) {
                const warning = document.createElement('div');
                warning.className = 'warning';
                warning.innerHTML = `
                    <h4>💻 桌面设备检测</h4>
                    <p>检测到您正在使用桌面设备。此指南专为移动端设计。桌面端请直接安装浏览器扩展。</p>
                `;
                document.querySelector('.content').insertBefore(warning, document.querySelector('.step-card'));
            }
        }

        // 页面加载时执行检测
        document.addEventListener('DOMContentLoaded', detectDevice);
    </script>
</body>
</html>
