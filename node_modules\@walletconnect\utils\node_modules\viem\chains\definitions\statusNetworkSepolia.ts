import { chainConfig } from '../../linea/chainConfig.js'
import { define<PERSON>hain } from '../../utils/chain/defineChain.js'

export const statusSepolia = /*#__PURE__*/ defineChain({
  ...chainConfig,
  id: 1_660_990_954,
  name: 'Status Network Sepolia',
  nativeCurrency: {
    decimals: 18,
    name: '<PERSON><PERSON>',
    symbol: 'ETH',
  },
  rpcUrls: {
    default: {
      http: ['https://public.sepolia.rpc.status.network'],
      webSocket: ['wss://public.sepolia.rpc.status.network/ws'],
    },
  },
  blockExplorers: {
    default: {
      name: 'Blockscout',
      url: 'https://sepoliascan.status.network',
    },
  },
  contracts: {
    multicall3: {
      address: '******************************************',
      blockCreated: 1_578_364,
    },
  },
  testnet: true,
})
