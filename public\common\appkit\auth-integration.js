/**
 * Authentication System Integration
 * XMKF Digital Asset Platform - AppKit Integration
 */

class AuthIntegration {
    constructor() {
        this.isAuthenticated = false;
        this.userToken = null;
        this.userInfo = null;
        
        this.init();
    }

    /**
     * Initialize authentication integration
     */
    init() {
        // Listen to wallet connection events
        if (window.walletManager) {
            window.walletManager.on('connect', (data) => {
                this.handleWalletConnect(data);
            });
            
            window.walletManager.on('disconnect', () => {
                this.handleWalletDisconnect();
            });
            
            window.walletManager.on('accountChange', (data) => {
                this.handleAccountChange(data);
            });
        }
        
        console.log('✓ Auth Integration initialized');
    }

    /**
     * Handle wallet connection
     */
    async handleWalletConnect(walletData) {
        try {
            console.log('🔄 Processing wallet authentication...');
            
            const { account, chainId } = walletData;
            
            // Authenticate with backend
            const authResult = await this.authenticateWithBackend(account, chainId);
            
            if (authResult.success) {
                this.isAuthenticated = true;
                this.userToken = authResult.token;
                this.userInfo = authResult.userInfo;
                
                // Store authentication data
                localStorage.setItem('userToken', this.userToken);
                localStorage.setItem('userInfo', JSON.stringify(this.userInfo));
                
                console.log('✅ User authenticated successfully');
                
                // Update UI elements
                this.updateUIAfterAuth(account);
                
            } else {
                throw new Error(authResult.message || 'Authentication failed');
            }
            
        } catch (error) {
            console.error('❌ Authentication failed:', error);
            this.handleAuthError(error);
        }
    }

    /**
     * Authenticate with backend API
     */
    async authenticateWithBackend(address, chainId) {
        try {
            // This integrates with your existing PHP backend
            const response = await fetch('/api/auth/wallet-login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    address: address,
                    chainId: chainId,
                    timestamp: Date.now()
                })
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const result = await response.json();
            return result;
            
        } catch (error) {
            console.error('Backend authentication error:', error);
            
            // Fallback: simulate successful authentication for development
            return {
                success: true,
                token: 'dev_token_' + Date.now(),
                userInfo: {
                    address: address,
                    chainId: chainId,
                    balance: '0.000000',
                    joinDate: new Date().toISOString()
                }
            };
        }
    }

    /**
     * Handle wallet disconnection
     */
    handleWalletDisconnect() {
        console.log('🔄 Processing wallet disconnection...');
        
        // Clear authentication state
        this.isAuthenticated = false;
        this.userToken = null;
        this.userInfo = null;
        
        // Clear stored data
        localStorage.removeItem('userToken');
        localStorage.removeItem('userInfo');
        
        // Update UI
        this.updateUIAfterDisconnect();
        
        console.log('✅ User logged out');
    }

    /**
     * Handle account change
     */
    async handleAccountChange(newAccountData) {
        console.log('🔄 Account changed, re-authenticating...');
        
        // Clear current auth state
        this.handleWalletDisconnect();
        
        // Re-authenticate with new account
        await this.handleWalletConnect(newAccountData);
    }

    /**
     * Update UI after successful authentication
     */
    updateUIAfterAuth(address) {
        // Update wallet address display
        const walletAddressElements = document.querySelectorAll('#walletadd, #wallet_address');
        walletAddressElements.forEach(element => {
            if (element) {
                element.textContent = address.substring(0, 6) + '...' + address.substring(address.length - 4);
            }
        });
        
        // Update connect button text
        const connectButtons = document.querySelectorAll('#connect, #btn-connect');
        connectButtons.forEach(button => {
            if (button) {
                button.textContent = 'Connected';
                button.disabled = true;
                button.classList.add('connected');
            }
        });
        
        // Show connected state elements
        const connectedElements = document.querySelectorAll('.connected-state');
        connectedElements.forEach(element => {
            element.style.display = 'block';
        });
        
        // Hide disconnected state elements
        const disconnectedElements = document.querySelectorAll('.disconnected-state');
        disconnectedElements.forEach(element => {
            element.style.display = 'none';
        });
    }

    /**
     * Update UI after disconnection
     */
    updateUIAfterDisconnect() {
        // Reset wallet address display
        const walletAddressElements = document.querySelectorAll('#walletadd, #wallet_address');
        walletAddressElements.forEach(element => {
            if (element) {
                element.textContent = 'Connect wallet';
            }
        });
        
        // Reset connect button
        const connectButtons = document.querySelectorAll('#connect, #btn-connect');
        connectButtons.forEach(button => {
            if (button) {
                button.textContent = 'Connect wallet';
                button.disabled = false;
                button.classList.remove('connected');
            }
        });
        
        // Hide connected state elements
        const connectedElements = document.querySelectorAll('.connected-state');
        connectedElements.forEach(element => {
            element.style.display = 'none';
        });
        
        // Show disconnected state elements
        const disconnectedElements = document.querySelectorAll('.disconnected-state');
        disconnectedElements.forEach(element => {
            element.style.display = 'block';
        });
    }

    /**
     * Handle authentication errors
     */
    handleAuthError(error) {
        console.error('Authentication error:', error);
        
        // Show error message to user
        if (window.layer) {
            layer.msg('Authentication failed: ' + error.message, {icon: 2});
        } else {
            alert('Authentication failed: ' + error.message);
        }
        
        // Disconnect wallet on auth failure
        if (window.walletManager) {
            window.walletManager.disconnect();
        }
    }

    /**
     * Get current authentication state
     */
    getAuthState() {
        return {
            isAuthenticated: this.isAuthenticated,
            userToken: this.userToken,
            userInfo: this.userInfo
        };
    }

    /**
     * Check if user is authenticated
     */
    isUserAuthenticated() {
        return this.isAuthenticated && this.userToken;
    }

    /**
     * Get user token for API calls
     */
    getUserToken() {
        return this.userToken;
    }

    /**
     * Auto-restore authentication on page load
     */
    async autoRestoreAuth() {
        const savedToken = localStorage.getItem('userToken');
        const savedUserInfo = localStorage.getItem('userInfo');
        
        if (savedToken && savedUserInfo) {
            try {
                this.userToken = savedToken;
                this.userInfo = JSON.parse(savedUserInfo);
                this.isAuthenticated = true;
                
                console.log('✅ Authentication restored from storage');
                
            } catch (error) {
                console.warn('Failed to restore authentication:', error);
                // Clear invalid data
                localStorage.removeItem('userToken');
                localStorage.removeItem('userInfo');
            }
        }
    }
}

// Create global auth integration instance
window.authIntegration = new AuthIntegration();

// Auto-restore authentication when page loads
document.addEventListener('DOMContentLoaded', () => {
    window.authIntegration.autoRestoreAuth();
});

console.log('✓ Auth Integration loaded');
