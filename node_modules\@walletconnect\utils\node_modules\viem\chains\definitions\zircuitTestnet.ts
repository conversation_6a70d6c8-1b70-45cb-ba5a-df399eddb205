import { chainConfig } from '../../op-stack/chainConfig.js'
import { define<PERSON>hain } from '../../utils/chain/defineChain.js'

const sourceId = 11_155_111 // sepolia

export const zircuitTestnet = /*#__PURE__*/ defineChain({
  ...chainConfig,
  id: 48899,
  name: 'Zircuit Testnet',
  nativeCurrency: { name: 'ETH', symbol: 'ETH', decimals: 18 },
  rpcUrls: {
    default: {
      http: [
        'https://testnet.zircuit.com',
        'https://zircuit1-testnet.p2pify.com',
        'https://zircuit1-testnet.liquify.com',
      ],
    },
  },
  blockExplorers: {
    default: {
      name: 'Zircuit Testnet Explorer',
      url: 'https://explorer.testnet.zircuit.com',
    },
  },
  contracts: {
    multicall3: {
      address: '******************************************',
      blockCreated: 6040287,
    },
    l2OutputOracle: {
      [sourceId]: {
        address: '******************************************',
      },
    },
    portal: {
      [sourceId]: {
        address: '******************************************',
      },
    },
    l1StandardBridge: {
      [sourceId]: {
        address: '******************************************',
      },
    },
  },
  testnet: true,
})
