# AppKit

The full stack toolkit to build onchain app UX.

Onboard millions of users to your app in minutes with social & email embedded wallets, web3 wallet login, crypto swaps, on-ramp and more.

🛝 [Demo](https://demo.reown.com) ・🧪 [Laboratory](https://appkit-lab.reown.com) ・📚 [Documentation](https://docs.reown.com/appkit/overview) ・💻 [AppKit Web Examples](https://github.com/reown-com/appkit-web-examples) ・🔗 [Website](https://reown.com/appkit) ・🛟 [Contact us on Discord](https://discord.gg/reown)

<p align="center">
  <img src="https://github.com/reown-com/appkit/blob/HEAD/.github/assets/header.png" alt="" border="0">
</p>

## Features

Refer to the "Features" section of the [AppKit docs](https://docs.reown.com/appkit/features).

- Swaps
- On-Ramp
- Multi Chain
- Multi Wallets
- Smart Accounts
- Telegram Mini Apps
- Sponsored Transactions
- Networks: EVM Chains, Solana, Bitcoin
- AppKit Core: Chain Agnostic
- Authentication: Email & Social Login, One-Click Auth & Sign with X (SIWX)

## AppKit Available SDKs

- [React](https://docs.reown.com/appkit/react/core/installation)
- [Next](https://docs.reown.com/appkit/next/core/installation)
- [Vue](https://docs.reown.com/appkit/vue/core/installation)
- [Nuxt](https://docs.reown.com/appkit/nuxt/core/installation)
- [Svelte](https://docs.reown.com/appkit/svelte/core/installation)
- [Javascript](https://docs.reown.com/appkit/javascript/core/installation)
- [React Native](https://docs.reown.com/appkit/react-native/core/installation)
- [Flutter](https://docs.reown.com/appkit/flutter/core/installation)
- [Android](https://docs.reown.com/appkit/android/core/installation)
- [iOS](https://docs.reown.com/appkit/ios/core/installation)
- [Unity](https://docs.reown.com/appkit/unity/core/installation)

> [!NOTE]
> If you are using Web3Modal v1–v5, please use our [migration guides](https://docs.reown.com/appkit/upgrade/to-reown-appkit-web#migrate-from-web3modal-v5-to-reown-appkit).
