<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AppKit Integration Test - XMKF Platform</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #8e5729;
        }
        
        .test-header h1 {
            color: #8e5729;
            margin: 0;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        
        .test-section h3 {
            color: #333;
            margin-top: 0;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-loading { background: #ffc107; }
        .status-success { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-pending { background: #6c757d; }
        
        .test-button {
            background: #8e5729;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .test-button:hover {
            background: #a66b3a;
            transform: translateY(-1px);
        }
        
        .test-button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        
        .log-container {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            line-height: 1.4;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        
        .log-success { color: #28a745; }
        .log-error { color: #dc3545; }
        .log-warning { color: #ffc107; }
        .log-info { color: #17a2b8; }
        
        .wallet-info {
            background: #e8f5e8;
            border: 1px solid #28a745;
            border-radius: 6px;
            padding: 15px;
            margin-top: 15px;
        }
        
        .network-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .network-card {
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .network-card:hover {
            border-color: #8e5729;
            background: #f8f9fa;
        }
        
        .network-card.active {
            border-color: #28a745;
            background: #e8f5e8;
        }
        
        .network-icon {
            width: 32px;
            height: 32px;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🧪 AppKit Integration Test Suite</h1>
            <p>XMKF Digital Asset Platform - Web3 Wallet Integration Testing</p>
        </div>

        <!-- System Status -->
        <div class="test-section">
            <h3>📊 System Status</h3>
            <div id="system-status">
                <div><span class="status-indicator status-loading" id="config-status"></span>Configuration: <span id="config-text">Loading...</span></div>
                <div><span class="status-indicator status-loading" id="appkit-status"></span>AppKit: <span id="appkit-text">Loading...</span></div>
                <div><span class="status-indicator status-loading" id="wallet-manager-status"></span>Wallet Manager: <span id="wallet-manager-text">Loading...</span></div>
                <div><span class="status-indicator status-loading" id="auth-status"></span>Authentication: <span id="auth-text">Loading...</span></div>
            </div>
        </div>

        <!-- Wallet Connection Tests -->
        <div class="test-section">
            <h3>🔗 Wallet Connection Tests</h3>
            <div>
                <button class="test-button" onclick="testWalletConnection()" id="connect-btn">Connect Wallet</button>
                <button class="test-button" onclick="testWalletDisconnection()" id="disconnect-btn" disabled>Disconnect Wallet</button>
                <button class="test-button" onclick="testAutoReconnect()">Test Auto-Reconnect</button>
            </div>
            
            <div class="wallet-info" id="wallet-info" style="display: none;">
                <h4>Connected Wallet Information</h4>
                <div><strong>Address:</strong> <span id="wallet-address">-</span></div>
                <div><strong>Network:</strong> <span id="wallet-network">-</span></div>
                <div><strong>Chain ID:</strong> <span id="wallet-chain-id">-</span></div>
                <div><strong>Balance:</strong> <span id="wallet-balance">-</span></div>
            </div>
        </div>

        <!-- Network Switching Tests -->
        <div class="test-section">
            <h3>🌐 Network Switching Tests</h3>
            <div class="network-grid">
                <div class="network-card" onclick="testNetworkSwitch(1)" data-chain-id="1">
                    <img src="public/erc/images/icon1.svg" alt="Ethereum" class="network-icon" onerror="this.style.display='none'">
                    <div><strong>Ethereum</strong></div>
                    <div>Chain ID: 1</div>
                </div>
                <div class="network-card" onclick="testNetworkSwitch(56)" data-chain-id="56">
                    <img src="public/erc/images/bnb.png" alt="BSC" class="network-icon" onerror="this.style.display='none'">
                    <div><strong>BSC</strong></div>
                    <div>Chain ID: 56</div>
                </div>
                <div class="network-card" onclick="testNetworkSwitch(137)" data-chain-id="137">
                    <img src="public/erc/images/polygon.png" alt="Polygon" class="network-icon" onerror="this.style.display='none'">
                    <div><strong>Polygon</strong></div>
                    <div>Chain ID: 137</div>
                </div>
                <div class="network-card" onclick="testNetworkSwitch(11155111)" data-chain-id="11155111">
                    <img src="public/erc/images/icon1.svg" alt="Sepolia" class="network-icon" onerror="this.style.display='none'">
                    <div><strong>Sepolia</strong></div>
                    <div>Chain ID: 11155111</div>
                </div>
            </div>
        </div>

        <!-- Authentication Tests -->
        <div class="test-section">
            <h3>🔐 Authentication Tests</h3>
            <div>
                <button class="test-button" onclick="testAuthentication()">Test Auth Integration</button>
                <button class="test-button" onclick="testTokenValidation()">Test Token Validation</button>
                <button class="test-button" onclick="testSessionRestore()">Test Session Restore</button>
            </div>
        </div>

        <!-- Performance Tests -->
        <div class="test-section">
            <h3>⚡ Performance Tests</h3>
            <div>
                <button class="test-button" onclick="testLoadingSpeed()">Test Loading Speed</button>
                <button class="test-button" onclick="testConnectionSpeed()">Test Connection Speed</button>
                <button class="test-button" onclick="testMemoryUsage()">Test Memory Usage</button>
            </div>
            <div id="performance-results" style="margin-top: 15px;"></div>
        </div>

        <!-- Test Log -->
        <div class="test-section">
            <h3>📝 Test Log</h3>
            <button class="test-button" onclick="clearLog()">Clear Log</button>
            <button class="test-button" onclick="exportLog()">Export Log</button>
            <div class="log-container" id="test-log"></div>
        </div>
    </div>

    <!-- Load AppKit Configuration -->
    <script src="public/common/appkit/config.js"></script>
    <script src="public/common/appkit/network-config.js"></script>
    <script src="public/common/appkit/appkit-loader.js"></script>
    <script src="public/common/appkit/wallet-manager.js"></script>
    <script src="public/common/appkit/auth-integration.js"></script>

    <script>
        // Test Suite JavaScript
        let testStartTime = Date.now();
        let currentWalletAddress = null;
        let currentChainId = null;

        // Initialize test suite
        document.addEventListener('DOMContentLoaded', async () => {
            log('🚀 Starting AppKit Integration Test Suite', 'info');
            
            try {
                await initializeTestSuite();
            } catch (error) {
                log('❌ Test suite initialization failed: ' + error.message, 'error');
            }
        });

        // Initialize test suite
        async function initializeTestSuite() {
            // Check configuration
            updateStatus('config', window.APPKIT_CONFIG ? 'success' : 'error', 
                        window.APPKIT_CONFIG ? 'Loaded' : 'Failed');
            
            // Wait for AppKit to load
            if (window.appKitLoader) {
                try {
                    await window.appKitLoader.load();
                    updateStatus('appkit', 'success', 'Loaded');
                } catch (error) {
                    updateStatus('appkit', 'error', 'Failed: ' + error.message);
                }
            }
            
            // Check wallet manager
            if (window.walletManager) {
                updateStatus('wallet-manager', 'success', 'Ready');
                setupWalletEventListeners();
            } else {
                updateStatus('wallet-manager', 'error', 'Not available');
            }
            
            // Check authentication
            if (window.authIntegration) {
                updateStatus('auth', 'success', 'Ready');
            } else {
                updateStatus('auth', 'error', 'Not available');
            }
            
            log('✅ Test suite initialization completed', 'success');
        }

        // Setup wallet event listeners
        function setupWalletEventListeners() {
            if (!window.walletManager) return;

            window.walletManager.on('connect', (data) => {
                currentWalletAddress = data.account;
                currentChainId = data.chainId;
                updateWalletInfo(data);
                updateConnectButtons(true);
                log(`✅ Wallet connected: ${data.account}`, 'success');
            });

            window.walletManager.on('disconnect', () => {
                currentWalletAddress = null;
                currentChainId = null;
                hideWalletInfo();
                updateConnectButtons(false);
                log('🔌 Wallet disconnected', 'info');
            });

            window.walletManager.on('chainChange', (chainId) => {
                currentChainId = chainId;
                updateNetworkDisplay(chainId);
                log(`🌐 Network changed to: ${getNetworkName(chainId)} (${chainId})`, 'info');
            });

            window.walletManager.on('error', (error) => {
                log(`❌ Wallet error: ${error.message}`, 'error');
            });
        }

        // Test wallet connection
        async function testWalletConnection() {
            log('🔄 Testing wallet connection...', 'info');
            
            try {
                const result = await window.walletManager.connect();
                log('✅ Wallet connection test passed', 'success');
                return result;
            } catch (error) {
                log(`❌ Wallet connection test failed: ${error.message}`, 'error');
                throw error;
            }
        }

        // Test wallet disconnection
        async function testWalletDisconnection() {
            log('🔄 Testing wallet disconnection...', 'info');
            
            try {
                await window.walletManager.disconnect();
                log('✅ Wallet disconnection test passed', 'success');
            } catch (error) {
                log(`❌ Wallet disconnection test failed: ${error.message}`, 'error');
            }
        }

        // Test network switching
        async function testNetworkSwitch(chainId) {
            log(`🔄 Testing network switch to ${getNetworkName(chainId)}...`, 'info');
            
            if (!currentWalletAddress) {
                log('⚠️ Please connect wallet first', 'warning');
                return;
            }
            
            try {
                await window.walletManager.switchNetwork(chainId);
                updateActiveNetwork(chainId);
                log(`✅ Network switch test passed: ${getNetworkName(chainId)}`, 'success');
            } catch (error) {
                log(`❌ Network switch test failed: ${error.message}`, 'error');
            }
        }

        // Test auto-reconnect
        async function testAutoReconnect() {
            log('🔄 Testing auto-reconnect functionality...', 'info');
            
            try {
                if (window.walletManager) {
                    await window.walletManager.autoReconnect();
                    log('✅ Auto-reconnect test completed', 'success');
                }
            } catch (error) {
                log(`❌ Auto-reconnect test failed: ${error.message}`, 'error');
            }
        }

        // Test authentication
        async function testAuthentication() {
            log('🔄 Testing authentication integration...', 'info');
            
            if (!currentWalletAddress) {
                log('⚠️ Please connect wallet first', 'warning');
                return;
            }
            
            try {
                if (window.authIntegration) {
                    const authState = window.authIntegration.getAuthState();
                    log(`✅ Authentication test passed. Authenticated: ${authState.isAuthenticated}`, 'success');
                }
            } catch (error) {
                log(`❌ Authentication test failed: ${error.message}`, 'error');
            }
        }

        // Test token validation
        async function testTokenValidation() {
            log('🔄 Testing token validation...', 'info');
            
            try {
                if (window.authIntegration) {
                    const token = window.authIntegration.getUserToken();
                    log(`✅ Token validation test passed. Token: ${token ? 'Present' : 'None'}`, 'success');
                }
            } catch (error) {
                log(`❌ Token validation test failed: ${error.message}`, 'error');
            }
        }

        // Test session restore
        async function testSessionRestore() {
            log('🔄 Testing session restore...', 'info');
            
            try {
                if (window.authIntegration) {
                    await window.authIntegration.autoRestoreAuth();
                    log('✅ Session restore test completed', 'success');
                }
            } catch (error) {
                log(`❌ Session restore test failed: ${error.message}`, 'error');
            }
        }

        // Performance tests
        async function testLoadingSpeed() {
            const startTime = performance.now();
            log('🔄 Testing loading speed...', 'info');
            
            try {
                // Simulate loading test
                await new Promise(resolve => setTimeout(resolve, 100));
                const endTime = performance.now();
                const loadTime = (endTime - startTime).toFixed(2);
                
                document.getElementById('performance-results').innerHTML += 
                    `<div>Loading Speed: ${loadTime}ms</div>`;
                log(`✅ Loading speed test: ${loadTime}ms`, 'success');
            } catch (error) {
                log(`❌ Loading speed test failed: ${error.message}`, 'error');
            }
        }

        async function testConnectionSpeed() {
            log('🔄 Testing connection speed...', 'info');
            
            if (!currentWalletAddress) {
                log('⚠️ Please connect wallet first', 'warning');
                return;
            }
            
            const startTime = performance.now();
            try {
                // Test connection speed by checking wallet state
                const state = window.walletManager.getState();
                const endTime = performance.now();
                const responseTime = (endTime - startTime).toFixed(2);
                
                document.getElementById('performance-results').innerHTML += 
                    `<div>Connection Response: ${responseTime}ms</div>`;
                log(`✅ Connection speed test: ${responseTime}ms`, 'success');
            } catch (error) {
                log(`❌ Connection speed test failed: ${error.message}`, 'error');
            }
        }

        async function testMemoryUsage() {
            log('🔄 Testing memory usage...', 'info');
            
            try {
                if (performance.memory) {
                    const memory = performance.memory;
                    const usedMB = (memory.usedJSHeapSize / 1024 / 1024).toFixed(2);
                    const totalMB = (memory.totalJSHeapSize / 1024 / 1024).toFixed(2);
                    
                    document.getElementById('performance-results').innerHTML += 
                        `<div>Memory Usage: ${usedMB}MB / ${totalMB}MB</div>`;
                    log(`✅ Memory usage test: ${usedMB}MB used`, 'success');
                } else {
                    log('⚠️ Memory API not available', 'warning');
                }
            } catch (error) {
                log(`❌ Memory usage test failed: ${error.message}`, 'error');
            }
        }

        // Utility functions
        function updateStatus(component, status, text) {
            const indicator = document.getElementById(`${component}-status`);
            const textElement = document.getElementById(`${component}-text`);
            
            if (indicator && textElement) {
                indicator.className = `status-indicator status-${status}`;
                textElement.textContent = text;
            }
        }

        function updateWalletInfo(data) {
            document.getElementById('wallet-info').style.display = 'block';
            document.getElementById('wallet-address').textContent = data.account;
            document.getElementById('wallet-network').textContent = getNetworkName(data.chainId);
            document.getElementById('wallet-chain-id').textContent = data.chainId;
            document.getElementById('wallet-balance').textContent = 'Loading...';
        }

        function hideWalletInfo() {
            document.getElementById('wallet-info').style.display = 'none';
        }

        function updateConnectButtons(connected) {
            document.getElementById('connect-btn').disabled = connected;
            document.getElementById('disconnect-btn').disabled = !connected;
        }

        function updateNetworkDisplay(chainId) {
            document.getElementById('wallet-chain-id').textContent = chainId;
            document.getElementById('wallet-network').textContent = getNetworkName(chainId);
            updateActiveNetwork(chainId);
        }

        function updateActiveNetwork(chainId) {
            document.querySelectorAll('.network-card').forEach(card => {
                card.classList.remove('active');
            });
            
            const activeCard = document.querySelector(`[data-chain-id="${chainId}"]`);
            if (activeCard) {
                activeCard.classList.add('active');
            }
        }

        function getNetworkName(chainId) {
            const networks = {
                1: 'Ethereum',
                56: 'BSC',
                137: 'Polygon',
                11155111: 'Sepolia'
            };
            return networks[chainId] || 'Unknown';
        }

        function log(message, type = 'info') {
            const logContainer = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            
            console.log(`[AppKit Test] ${message}`);
        }

        function clearLog() {
            document.getElementById('test-log').innerHTML = '';
            log('📝 Test log cleared', 'info');
        }

        function exportLog() {
            const logContent = document.getElementById('test-log').textContent;
            const blob = new Blob([logContent], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `appkit-test-log-${new Date().toISOString().slice(0, 19)}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            log('📄 Test log exported', 'success');
        }

        console.log('✓ AppKit Test Suite loaded');
    </script>
</body>
</html>
