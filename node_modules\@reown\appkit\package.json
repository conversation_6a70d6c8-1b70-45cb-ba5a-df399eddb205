{"name": "@reown/appkit", "version": "1.7.11", "type": "module", "main": "./dist/esm/exports/index.js", "types": "./dist/types/exports/index.d.ts", "files": ["dist", "scripts", "!tsconfig.tsbuildinfo", "README.md"], "exports": {".": {"types": "./dist/types/exports/index.d.ts", "import": "./dist/esm/exports/index.js", "default": "./dist/esm/exports/index.js"}, "./react": {"types": "./dist/types/exports/react.d.ts", "import": "./dist/esm/exports/react.js", "default": "./dist/esm/exports/react.js"}, "./vue": {"types": "./dist/types/exports/vue.d.ts", "import": "./dist/esm/exports/vue.js", "default": "./dist/esm/exports/vue.js"}, "./library/react": {"types": "./dist/types/exports/library/react.d.ts", "import": "./dist/esm/exports/library/react.js", "default": "./dist/esm/exports/library/react.js"}, "./library/vue": {"types": "./dist/types/exports/library/vue.d.ts", "import": "./dist/esm/exports/library/vue.js", "default": "./dist/esm/exports/library/vue.js"}, "./store": {"types": "./dist/types/exports/store.d.ts", "import": "./dist/esm/exports/store.js", "default": "./dist/esm/exports/store.js"}, "./utils": {"types": "./dist/types/exports/utils.d.ts", "import": "./dist/esm/exports/utils.js", "default": "./dist/esm/exports/utils.js"}, "./constants": {"types": "./dist/types/exports/constants.d.ts", "import": "./dist/esm/exports/constants.js", "default": "./dist/esm/exports/constants.js"}, "./networks": {"types": "./dist/types/exports/networks.d.ts", "import": "./dist/esm/exports/networks.js", "default": "./dist/esm/exports/networks.js"}, "./auth-provider": {"types": "./dist/types/exports/auth-provider.d.ts", "import": "./dist/esm/exports/auth-provider.js", "default": "./dist/esm/exports/auth-provider.js"}, "./adapters": {"types": "./dist/types/exports/adapters.d.ts", "import": "./dist/esm/exports/adapters.js", "default": "./dist/esm/exports/adapters.js"}, "./connectors": {"types": "./dist/types/exports/connectors.d.ts", "import": "./dist/esm/exports/connectors.js", "default": "./dist/esm/exports/connectors.js"}, "./connections": {"types": "./dist/types/exports/connections.d.ts", "import": "./dist/esm/exports/connections.js", "default": "./dist/esm/exports/connections.js"}, "./core": {"types": "./dist/types/exports/core.d.ts", "import": "./dist/esm/exports/core.js", "default": "./dist/esm/exports/core.js"}, "./vue-core": {"types": "./dist/types/exports/vue-core.d.ts", "import": "./dist/esm/exports/vue-core.js", "default": "./dist/esm/exports/vue-core.js"}, "./react-core": {"types": "./dist/types/exports/react-core.d.ts", "import": "./dist/esm/exports/react-core.js", "default": "./dist/esm/exports/react-core.js"}}, "typesVersions": {"*": {"react": ["./dist/types/exports/react.d.ts"], "vue": ["./dist/types/exports/vue.d.ts"], "library/react": ["./dist/types/exports/library/react.d.ts"], "library/vue": ["./dist/types/exports/library/vue.d.ts"], "store": ["./dist/types/exports/store.d.ts"], "networks": ["./dist/types/exports/networks.d.ts"], "auth-provider": ["./dist/types/exports/auth-provider.d.ts"], "adapters": ["./dist/types/exports/adapters.d.ts"]}}, "dependencies": {"@walletconnect/types": "2.21.3", "@walletconnect/universal-provider": "2.21.3", "bs58": "6.0.0", "semver": "7.7.2", "valtio": "1.13.2", "viem": ">=2.31.3", "@reown/appkit-common": "1.7.11", "@reown/appkit-controllers": "1.7.11", "@reown/appkit-polyfills": "1.7.11", "@reown/appkit-scaffold-ui": "1.7.11", "@reown/appkit-ui": "1.7.11", "@reown/appkit-utils": "1.7.11", "@reown/appkit-wallet": "1.7.11", "@reown/appkit-pay": "1.7.11"}, "devDependencies": {"@types/react": "19.1.3", "@types/react-dom": "19.1.3", "@vitest/coverage-v8": "2.1.9", "@vue/runtime-core": "3.4.3", "@walletconnect/types": "2.21.3", "@walletconnect/universal-provider": "2.21.3", "@vue/test-utils": "2.4.6", "react": "19.1.0", "react-dom": "19.1.0", "vitest": "3.1.3", "vue": "3.x", "@reown/appkit-siwe": "1.7.11"}, "author": "Reown (https://discord.gg/reown)", "license": "Apache-2.0", "homepage": "https://github.com/reown-com/appkit", "repository": {"type": "git", "url": "git+https://github.com/reown-com/appkit.git"}, "bugs": {"url": "https://github.com/reown-com/appkit/issues"}, "keywords": ["appkit", "wallet", "onboarding", "reown", "dapps", "web3", "wagmi", "ethereum", "solana", "bitcoin"], "scripts": {"build:clean": "rm -rf dist", "build": "tsc --build tsconfig.build.json", "watch": "tsc --watch", "typecheck": "tsc --noEmit", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "test": "vitest run --coverage.enabled=true -- coverage.reporter=json --coverage.reporter=json-summary --coverage.reportOnFailure=true", "postinstall": "node scripts/appkit-version-check.js"}}