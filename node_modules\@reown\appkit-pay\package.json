{"name": "@reown/appkit-pay", "version": "1.7.11", "type": "module", "main": "./dist/esm/exports/index.js", "types": "./dist/types/exports/index.d.ts", "files": ["dist", "!tsconfig.tsbuildinfo", "README.md"], "exports": {".": {"types": "./dist/types/exports/index.d.ts", "import": "./dist/esm/exports/index.js", "default": "./dist/esm/exports/index.js"}, "./react": {"types": "./dist/types/exports/react.d.ts", "import": "./dist/esm/exports/react.js", "default": "./dist/esm/exports/react.js"}}, "dependencies": {"lit": "3.3.0", "valtio": "1.13.2", "@reown/appkit-common": "1.7.11", "@reown/appkit-controllers": "1.7.11", "@reown/appkit-utils": "1.7.11", "@reown/appkit-ui": "1.7.11"}, "author": "Reown (https://discord.gg/reown)", "license": "Apache-2.0", "homepage": "https://github.com/reown-com/appkit", "repository": {"type": "git", "url": "git+https://github.com/reown-com/appkit.git"}, "bugs": {"url": "https://github.com/reown-com/appkit/issues"}, "devDependencies": {"@open-wc/testing": "4.0.0", "@types/react": "19.1.3", "@vitest/coverage-v8": "2.1.9", "vitest": "3.1.3"}, "keywords": ["appkit", "wallet", "onboarding", "reown", "dapps", "web3", "wagmi", "ethereum", "solana", "bitcoin"], "scripts": {"build:clean": "rm -rf dist", "build": "tsc --build", "watch": "tsc --watch", "test": "vitest run --coverage.enabled=true -- coverage.reporter=json --coverage.reporter=json-summary --coverage.reportOnFailure=true", "typecheck": "tsc --noEmit", "lint": "eslint . --ext .js,.jsx,.ts,.tsx"}}