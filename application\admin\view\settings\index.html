{extend name="public:template" /}
{block name="content"}
<div class="section-body">
    <h2 class="section-title">系统设置</h2>
    <div style="text-align: center;font-size: 23px;color: red">（系统设置）</div>
    <div class="row">
        <div class="col-md-12">
            <form id="setting-form" autocomplete=off class="needs-validation" novalidate="">
                <div class="card" id="settings-card">
                    <div class="card-body">



                        {volist name="list" id="vo" }
                            <div class="form-group row align-items-center">
                                <label class="form-control-label col-sm-3 text-md-right">{$vo.name} {if condition="$vo.remark"}（{$vo.remark}）{/if}</label>
                                <div class="col-sm-6 col-md-9 row no-gutters">
                                    <input type="text" name="{$vo.key}" class="form-control" value="{$vo.value}">
                                </div>
                            </div>
                        {/volist}



                        <div class="card-footer bg-whitesmoke text-md-right">
                            <button  type="button" class="btn btn-primary" id="save-btn">保存</button>
                        </div>
                    </div>
            </form>
        </div>
    </div>
</div>
{/block}

{block name="extend-script"}
<script type="text/javascript">
    $("#save-btn").click(function() {
        let save_button = $(this).find('#save-btn'),
            output_status = $("#output-status"),
            card = $('#settings-card');

        let card_progress = $.cardProgress(card, {
            spinner: false
        });
        save_button.addClass('btn-progress');
        output_status.html('');

        $.ajax({
            url: "{:url('Settings/update')}",
            method: 'post',
            data: $("#setting-form").serialize(),
            dataType: 'json',
            success(res) {
                if (res.success === true) {
                    swal('操作成功', {buttons: false, icon: 'success'});
                    setTimeout(function () { location.reload() }, 1500)
                }
                if (res.success === false) swal('出现错误', res.err_msg, 'error');
            }
        })

        return false;
    });
</script>
{/block}

