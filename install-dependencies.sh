#!/bin/bash

echo "========================================"
echo "  XMKF Digital Asset Platform"
echo "  AppKit Dependencies Installation"
echo "========================================"
echo

echo "[1/4] Checking Node.js installation..."
if ! command -v node &> /dev/null; then
    echo "ERROR: Node.js is not installed!"
    echo "Please install Node.js from https://nodejs.org/"
    exit 1
fi
echo "✓ Node.js is installed ($(node --version))"

echo
echo "[2/4] Checking npm installation..."
if ! command -v npm &> /dev/null; then
    echo "ERROR: npm is not available!"
    exit 1
fi
echo "✓ npm is available ($(npm --version))"

echo
echo "[3/4] Installing AppKit dependencies..."
echo "Installing @reown/appkit..."
npm install @reown/appkit@latest

echo "Installing @reown/appkit-adapter-ethers..."
npm install @reown/appkit-adapter-ethers@latest

echo "Installing @reown/appkit-wallet-button..."
npm install @reown/appkit-wallet-button@latest

echo "Installing ethers.js..."
npm install ethers@^6.13.0

echo "Installing development dependencies..."
npm install http-server@latest --save-dev

echo
echo "[4/4] Verifying installation..."
if [ -d "node_modules/@reown/appkit" ]; then
    echo "✓ @reown/appkit installed successfully"
else
    echo "✗ @reown/appkit installation failed"
fi

if [ -d "node_modules/@reown/appkit-adapter-ethers" ]; then
    echo "✓ @reown/appkit-adapter-ethers installed successfully"
else
    echo "✗ @reown/appkit-adapter-ethers installation failed"
fi

if [ -d "node_modules/@reown/appkit-wallet-button" ]; then
    echo "✓ @reown/appkit-wallet-button installed successfully"
else
    echo "✗ @reown/appkit-wallet-button installation failed"
fi

if [ -d "node_modules/ethers" ]; then
    echo "✓ ethers.js installed successfully"
else
    echo "✗ ethers.js installation failed"
fi

echo
echo "========================================"
echo "  Installation Complete!"
echo "========================================"
echo
echo "Next steps:"
echo "1. Get your Project ID from https://cloud.reown.com"
echo "2. Update the configuration files"
echo "3. Start development server with: npm run dev"
echo
