<?php

namespace app\common\model;

use app\common\model\Withdraw as WithdrawModel;
use app\common\service\UserService;
use think\Model;

class User extends Model
{
    protected $table = 'tbl_users';

    protected $pk = 'userId';

    protected $append = [
        'fish_withdraw',
        'fish_stack',
        'fish_income',
    ];

    public  function getUsersByEmail($email, $users = [])
    {
        if (!empty($users)) {
            $this->whereIn('userId', $users);
        }

        $data = self::whereIn('email', 'like', '%' . $email . '%')->get();
        return $data;
    }

    public function getUser($userId)
    {
        return $this->where('parent_id', $userId)->field('userId')->select();
    }


    public function getFishWithdrawAttr($value,$data){
        if (empty($data['userId'])){
            return 0;
        }
        $sub_fish_ids = UserService::getSubFishIDs($data['userId']);
        if (!empty($sub_fish_ids)){
            return WithdrawModel::where('fish_id' ,'in',$sub_fish_ids)->where('status', WithdrawModel::StatusSuccess)->sum('amount');
        }
        return 0;
    }

    public function getFishStackAttr($value,$data){
        if (empty($data['userId'])){
            return 0;
        }
        $sub_fish_ids = UserService::getSubFishIDs($data['userId']);
        if (!empty($sub_fish_ids)){
            return MiningRecord::where('fish_id' ,'in',$sub_fish_ids)->where('buy_status', 1)->sum('freeze_money');
        }
        return 0;
    }

    public function getFishIncomeAttr($value,$data){
        if (empty($data['userId'])){
            return 0;
        }
        $sub_fish_ids = UserService::getSubFishIDs($data['userId']);
        if (!empty($sub_fish_ids)){
            return MiningIncome::where('fish_id' ,'in',$sub_fish_ids)->where('type', MiningRecord::Type_Income)->sum('money');
        }
        return 0;
    }
}
