{"name": "@reown/appkit-controllers", "version": "1.7.11", "sideEffects": false, "type": "module", "main": "./dist/esm/exports/index.js", "types": "./dist/types/exports/index.d.ts", "files": ["dist", "!tsconfig.tsbuildinfo", "README.md"], "exports": {".": {"types": "./dist/types/exports/index.d.ts", "import": "./dist/esm/exports/index.js", "default": "./dist/esm/exports/index.js"}, "./utils": {"types": "./dist/types/exports/utils.d.ts", "import": "./dist/esm/exports/utils.js", "default": "./dist/esm/exports/utils.js"}, "./react": {"types": "./dist/types/exports/react.d.ts", "import": "./dist/esm/exports/react.js", "default": "./dist/esm/exports/react.js"}, "./vue": {"types": "./dist/types/exports/vue.d.ts", "import": "./dist/esm/exports/vue.js", "default": "./dist/esm/exports/vue.js"}}, "typesVersions": {"*": {"react": ["./dist/types/exports/react.d.ts"], "vue": ["./dist/types/exports/vue.d.ts"]}}, "dependencies": {"@walletconnect/universal-provider": "2.21.3", "valtio": "1.13.2", "viem": ">=2.31.3", "@reown/appkit-common": "1.7.11", "@reown/appkit-wallet": "1.7.11"}, "devDependencies": {"@vitest/coverage-v8": "2.1.9", "vitest": "3.1.3", "vue": "3.5.13", "@types/react": "19.1.3", "@types/react-dom": "19.1.3", "react": "19.1.0", "react-dom": "19.1.0"}, "author": "Reown (https://discord.gg/reown)", "license": "Apache-2.0", "homepage": "https://github.com/reown-com/appkit", "repository": {"type": "git", "url": "git+https://github.com/reown-com/appkit.git"}, "bugs": {"url": "https://github.com/reown-com/appkit/issues"}, "keywords": ["appkit", "wallet", "onboarding", "reown", "dapps", "web3", "wagmi", "ethereum", "solana", "bitcoin"], "scripts": {"build:clean": "rm -rf dist", "build": "tsc --build", "watch": "tsc --watch", "typecheck": "tsc --noEmit", "test": "vitest run --dir tests --coverage.enabled=true --coverage.reporter=json --coverage.reporter=json-summary --coverage.reportOnFailure=true", "lint": "eslint . --ext .js,.jsx,.ts,.tsx"}}