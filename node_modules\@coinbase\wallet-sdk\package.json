{"name": "@coinbase/wallet-sdk", "version": "4.3.0", "description": "Coinbase Wallet JavaScript SDK", "keywords": ["coinbase", "coinbasewallet", "eth", "ethereum", "wallet", "walletlink", "web3"], "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "sideEffects": false, "repository": "https://github.com/coinbase/coinbase-wallet-sdk.git", "author": "Coinbase, Inc.", "license": "Apache-2.0", "scripts": {"pretest": "node compile-assets.cjs", "test": "vitest", "test:coverage": "yarn test:unit && open coverage/lcov-report/index.html", "prebuild": "rm -rf ./dist && node -p \"'export const VERSION = \\'' + require('./package.json').version + '\\';\\nexport const NAME = \\'' + require('./package.json').name + '\\';'\" > src/sdk-info.ts", "build": "node compile-assets.cjs && tsc -p ./tsconfig.build.json && tsc-alias && cp -a src/vendor-js dist", "dev": "yarn build && tsc --watch & nodemon --watch dist --delay 1 --exec tsc-alias", "typecheck": "tsc --noEmit", "lint": "eslint . --ext .ts,.tsx --fix", "size": "size-limit"}, "dependencies": {"@noble/hashes": "^1.4.0", "clsx": "^1.2.1", "eventemitter3": "^5.0.1", "preact": "^10.24.2"}, "devDependencies": {"@size-limit/preset-big-lib": "^11.1.6", "@testing-library/jest-dom": "^6.5.0", "@testing-library/preact": "^3.2.4", "@types/node": "^14.18.54", "@typescript-eslint/eslint-plugin": "^6.2.0", "@typescript-eslint/parser": "^6.2.0", "eslint": "^8.45.0", "eslint-config-preact": "^1.3.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-simple-import-sort": "^10.0.0", "eslint-plugin-unused-imports": "^3.0.0", "glob": "^11.0.0", "jest-websocket-mock": "^2.4.0", "jsdom": "^25.0.1", "nodemon": "^3.1.0", "prettier": "^3.0.0", "sass": "^1.64.1", "size-limit": "^11.1.6", "tsc-alias": "^1.8.8", "tslib": "^2.6.0", "typescript": "^5.1.6", "vitest": "^2.1.2"}, "size-limit": [{"path": "./dist/index.js", "limit": "31 KB", "import": "*"}]}