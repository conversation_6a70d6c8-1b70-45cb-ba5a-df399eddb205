import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js'

export const thai<PERSON>hain = /*#__PURE__*/ define<PERSON>hain({
  id: 7,
  name: '<PERSON><PERSON><PERSON><PERSON>',
  nativeCurrency: { name: 'T<PERSON>', symbol: 'TCH', decimals: 18 },
  rpcUrls: {
    default: {
      http: ['https://rpc.thaichain.org'],
    },
  },
  blockExplorers: {
    default: {
      name: 'Blockscout',
      url: 'https://exp.thaichain.org',
      apiUrl: 'https://exp.thaichain.org/api',
    },
  },
  contracts: {
    multicall3: {
      address: '0x0DaD6130e832c21719C5CE3bae93454E16A84826',
      blockCreated: 4806386,
    },
  },
  testnet: false,
})
