import { chainConfig } from '../../op-stack/chainConfig.js'
import { define<PERSON>hain } from '../../utils/chain/defineChain.js'

const sourceId = 11_155_111 // sepolia

export const zircuitGarfieldTestnet = /*#__PURE__*/ defineChain({
  ...chainConfig,
  id: 48898,
  name: 'Zircuit Garfield Testnet',
  nativeCurrency: { name: 'ETH', symbol: 'ETH', decimals: 18 },
  rpcUrls: {
    default: {
      http: ['https://garfield-testnet.zircuit.com/'],
    },
  },
  blockExplorers: {
    default: {
      name: 'Zircuit Garfield Testnet Explorer',
      url: 'https://explorer.garfield-testnet.zircuit.com',
    },
  },
  contracts: {
    multicall3: {
      address: '******************************************',
    },
    l2OutputOracle: {
      [sourceId]: {
        address: '******************************************',
      },
    },
    portal: {
      [sourceId]: {
        address: '******************************************',
      },
    },
    l1StandardBridge: {
      [sourceId]: {
        address: '******************************************',
      },
    },
  },
  testnet: true,
})
