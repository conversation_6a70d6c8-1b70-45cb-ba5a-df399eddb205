export declare const tronShasta: {
    blockExplorers: {
        readonly default: {
            readonly name: "Trons<PERSON>";
            readonly url: "https://shasta.tronscan.org";
        };
    };
    blockTime?: number | undefined | undefined;
    contracts?: {
        [x: string]: import("../../index.js").ChainContract | {
            [sourceId: number]: import("../../index.js").ChainContract | undefined;
        } | undefined;
        ensRegistry?: import("../../index.js").ChainContract | undefined;
        ensUniversalResolver?: import("../../index.js").ChainContract | undefined;
        multicall3?: import("../../index.js").ChainContract | undefined;
        universalSignatureVerifier?: import("../../index.js").ChainContract | undefined;
    } | undefined;
    ensTlds?: readonly string[] | undefined;
    id: 2494104990;
    name: "Tron Shasta";
    nativeCurrency: {
        readonly name: "TRO<PERSON>";
        readonly symbol: "TRX";
        readonly decimals: 6;
    };
    rpcUrls: {
        readonly default: {
            readonly http: readonly ["https://api.shasta.trongrid.io/jsonrpc"];
        };
    };
    sourceId?: number | undefined | undefined;
    testnet: true;
    custom?: Record<string, unknown> | undefined;
    fees?: import("../../index.js").ChainFees<undefined> | undefined;
    formatters?: undefined;
    serializers?: import("../../index.js").ChainSerializers<undefined, import("../../index.js").TransactionSerializable> | undefined;
};
//# sourceMappingURL=tronShasta.d.ts.map