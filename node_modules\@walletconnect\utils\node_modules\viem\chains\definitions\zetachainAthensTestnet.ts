import { define<PERSON><PERSON><PERSON> } from '../../utils/chain/defineChain.js'

export const zetachainAthensTestnet = /*#__PURE__*/ define<PERSON>hain({
  id: 7001,
  name: 'ZetaChain Athens Testnet',
  nativeCurrency: {
    decimals: 18,
    name: '<PERSON><PERSON>',
    symbol: 'aZETA',
  },
  rpcUrls: {
    default: {
      http: ['https://zetachain-athens-evm.blockpi.network/v1/rpc/public'],
    },
  },
  contracts: {
    multicall3: {
      address: '0xcA11bde05977b3631167028862bE2a173976CA11',
      blockCreated: 2715217,
    },
  },
  blockExplorers: {
    default: {
      name: '<PERSON>eta<PERSON><PERSON>',
      url: 'https://athens.explorer.zetachain.com',
    },
  },
  testnet: true,
})
