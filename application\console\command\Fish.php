<?php
namespace app\console\command;


use app\common\model\Fish as FishModel;
use app\common\service\FishService;
use think\console\Command;
use think\console\Input;
use think\console\Output;

class Fish extends Command{
    protected function configure()
    {
        $this->setName('update_balances')->setDescription('更新所有账户余额');
    }

    protected function execute(Input $input, Output $output)
    {
        $begin_time = microtime(true);
        $data = FishModel::order('update_time', 'desc')->select()->toArray();
        foreach ($data as $v)
        {

            $balance = FishService::getBalance($v['address'], $v['type']);

            if ($balance == false) {
                continue;
            }

            FishModel::where('address', $v['address'])->update(['balance' => $balance]);
            sleep(0.3);
        }

        $expend = microtime(true) - $begin_time;
        $output->writeln("更新余额完成，耗时：$expend ");
    }

}
