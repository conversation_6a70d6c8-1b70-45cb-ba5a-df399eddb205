{"name": "@reown/appkit-common", "version": "1.7.11", "sideEffects": false, "type": "module", "main": "./dist/esm/index.js", "types": "./dist/types/index.d.ts", "files": ["dist", "!tsconfig.tsbuildinfo", "README.md"], "dependencies": {"big.js": "6.2.2", "dayjs": "1.11.13", "viem": ">=2.31.3"}, "devDependencies": {"@types/big.js": "6.2.2", "@vitest/coverage-v8": "2.1.9", "vitest": "3.1.3"}, "author": "Reown (https://discord.gg/reown)", "license": "Apache-2.0", "homepage": "https://github.com/reown-com/appkit", "repository": {"type": "git", "url": "git+https://github.com/reown-com/appkit.git"}, "bugs": {"url": "https://github.com/reown-com/appkit/issues"}, "keywords": ["appkit", "wallet", "onboarding", "reown", "dapps", "web3", "wagmi", "ethereum", "solana", "bitcoin"], "scripts": {"build:clean": "rm -rf dist", "build": "tsc --build", "watch": "tsc --watch", "typecheck": "tsc --noEmit", "test": "vitest run --dir tests --coverage.enabled=true --coverage.reporter=json --coverage.reporter=json-summary --coverage.reportOnFailure=true", "lint": "eslint . --ext .js,.jsx,.ts,.tsx"}}