# 🚀 XMKF Digital Asset Platform - AppKit Setup Guide

## 📋 Overview
This guide will help you complete the setup of Reown AppKit integration for the XMKF Digital Asset Platform.

## ✅ Completed Steps
- [x] Created project directory structure
- [x] Prepared configuration files
- [x] Created wallet management components
- [x] Set up authentication integration
- [x] Created unified wallet button component

## 🔧 Next Steps Required

### 1. Register Reown Cloud Project
1. Visit: https://cloud.reown.com
2. Create account or sign in
3. Click "Create Project"
4. Configure project:
   - **Name**: XMKF Digital Asset Platform
   - **Type**: AppKit
   - **Platform**: Web
   - **Networks**: Ethereum, BSC, Polygon

### 2. Update Configuration
After getting your Project ID, update the following file:
```javascript
// File: public/common/appkit/config.js
const PROJECT_ID = 'YOUR_ACTUAL_PROJECT_ID_HERE'; // Replace this
```

### 3. Install Dependencies
Run one of these commands in your project root:

**Windows:**
```bash
install-dependencies.bat
```

**Linux/Mac:**
```bash
chmod +x install-dependencies.sh
./install-dependencies.sh
```

**Manual Installation:**
```bash
npm install @reown/appkit@latest
npm install @reown/appkit-adapter-ethers@latest
npm install @reown/appkit-wallet-button@latest
npm install ethers@^6.13.0
```

### 4. Update Infura Keys (Optional)
For better performance, get Infura API keys:
1. Visit: https://infura.io
2. Create project
3. Update in `public/common/appkit/network-config.js`:
```javascript
rpcUrl: 'https://mainnet.infura.io/v3/YOUR_INFURA_KEY'
```

## 🔗 Integration Steps

### 1. Add to HTML Pages
Add these scripts to your HTML pages (before closing `</body>` tag):

```html
<!-- AppKit Configuration -->
<script src="../common/appkit/config.js"></script>
<script src="../common/appkit/network-config.js"></script>
<script src="../common/appkit/wallet-manager.js"></script>
<script src="../common/appkit/auth-integration.js"></script>

<!-- AppKit Libraries (CDN) -->
<script src="https://unpkg.com/@reown/appkit@latest/dist/index.js"></script>
<script src="https://unpkg.com/@reown/appkit-adapter-ethers@latest/dist/index.js"></script>
```

### 2. Replace Wallet Connect Buttons
Replace existing wallet connect buttons with:

```html
<!-- Include the wallet button component -->
<div id="wallet-component-container"></div>
<script>
    // Load wallet button component
    fetch('../common/components/wallet-button.html')
        .then(response => response.text())
        .then(html => {
            document.getElementById('wallet-component-container').innerHTML = html;
        });
</script>
```

### 3. Update Existing JavaScript
Replace existing wallet connection code:

**Old Code:**
```javascript
async function onConnect() {
    // Old Web3Modal code
}
```

**New Code:**
```javascript
async function onConnect() {
    if (window.walletManager) {
        await window.walletManager.connect();
    }
}
```

## 🧪 Testing

### 1. Start Development Server
```bash
npm run dev
```

### 2. Test Wallet Connections
- MetaMask connection
- WalletConnect QR code
- Network switching
- Account switching

### 3. Verify Integration
- Check browser console for errors
- Test on different devices
- Verify authentication flow

## 📁 File Structure
```
public/
├── common/
│   ├── appkit/
│   │   ├── config.js              ✅ Created
│   │   ├── wallet-manager.js      ✅ Created
│   │   ├── network-config.js      ✅ Created
│   │   └── auth-integration.js    ✅ Created
│   └── components/
│       └── wallet-button.html     ✅ Created
├── package.json                   ✅ Created
├── install-dependencies.bat       ✅ Created
└── install-dependencies.sh        ✅ Created
```

## 🔍 Troubleshooting

### Common Issues:
1. **"PROJECT_ID not set"** - Update config.js with your actual Project ID
2. **"AppKit not loaded"** - Check CDN links and internet connection
3. **"Network not supported"** - Verify network configuration
4. **"Connection failed"** - Check wallet extension installation

### Debug Mode:
Add to config.js for debugging:
```javascript
window.APPKIT_DEBUG = true;
```

## 📞 Support
- Check browser console for detailed error messages
- Verify all dependencies are installed
- Ensure Project ID is correctly configured
- Test with different wallets and networks

## 🎯 Next Phase
After completing setup:
1. Test all wallet connections
2. Verify multi-chain functionality
3. Update remaining pages
4. Performance optimization
5. Production deployment

---

**Status**: ✅ Infrastructure Ready - Awaiting Project ID Configuration
