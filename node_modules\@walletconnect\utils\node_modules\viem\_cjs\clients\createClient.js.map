{"version": 3, "file": "createClient.js", "sourceRoot": "", "sources": ["../../clients/createClient.ts"], "names": [], "mappings": ";;AAuNA,oCAsDC;AAMD,8BAEC;AAlRD,uEAG0C;AAc1C,4CAAqC;AAmMrC,SAAgB,YAAY,CAAC,UAAwB;IACnD,MAAM,EACJ,KAAK,EACL,KAAK,EACL,QAAQ,EACR,GAAG,GAAG,MAAM,EACZ,IAAI,GAAG,aAAa,EACpB,IAAI,GAAG,MAAM,GACd,GAAG,UAAU,CAAA;IAEd,MAAM,SAAS,GAAG,KAAK,EAAE,SAAS,IAAI,MAAM,CAAA;IAE5C,MAAM,sBAAsB,GAAG,IAAI,CAAC,GAAG,CACrC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,EACxC,KAAK,CACN,CAAA;IACD,MAAM,eAAe,GAAG,UAAU,CAAC,eAAe,IAAI,sBAAsB,CAAA;IAC5E,MAAM,SAAS,GAAG,UAAU,CAAC,SAAS,IAAI,eAAe,CAAA;IAEzD,MAAM,OAAO,GAAG,UAAU,CAAC,OAAO;QAChC,CAAC,CAAC,IAAA,8BAAY,EAAC,UAAU,CAAC,OAAO,CAAC;QAClC,CAAC,CAAC,SAAS,CAAA;IACb,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,UAAU,CAAC,SAAS,CAAC;QACtD,KAAK;QACL,eAAe;KAChB,CAAC,CAAA;IACF,MAAM,SAAS,GAAG,EAAE,GAAG,MAAM,EAAE,GAAG,KAAK,EAAE,CAAA;IAEzC,MAAM,MAAM,GAAG;QACb,OAAO;QACP,KAAK;QACL,SAAS;QACT,QAAQ;QACR,KAAK;QACL,GAAG;QACH,IAAI;QACJ,eAAe;QACf,OAAO;QACP,SAAS;QACT,IAAI;QACJ,GAAG,EAAE,IAAA,YAAG,GAAE;KACX,CAAA;IAED,SAAS,MAAM,CAAC,IAAmB;QAEjC,OAAO,CAAC,QAAkB,EAAE,EAAE;YAC5B,MAAM,QAAQ,GAAG,QAAQ,CAAC,IAAI,CAAa,CAAA;YAC3C,KAAK,MAAM,GAAG,IAAI,MAAM;gBAAE,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAA;YAC9C,MAAM,QAAQ,GAAG,EAAE,GAAG,IAAI,EAAE,GAAG,QAAQ,EAAE,CAAA;YACzC,OAAO,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,EAAE,MAAM,EAAE,MAAM,CAAC,QAAe,CAAC,EAAE,CAAC,CAAA;QACrE,CAAC,CAAA;IACH,CAAC;IAED,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,CAAQ,EAAE,CAAC,CAAA;AACjE,CAAC;AAMD,SAAgB,SAAS;IACvB,OAAO,IAAW,CAAA;AACpB,CAAC"}