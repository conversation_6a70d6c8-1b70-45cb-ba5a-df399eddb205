export declare const polynomialSepolia: {
    blockExplorers: {
        readonly default: {
            readonly name: "Polynomial Scan";
            readonly url: "https://sepolia.polynomialscan.io";
        };
    };
    blockTime?: number | undefined | undefined;
    contracts: {
        readonly multicall3: {
            readonly address: "******************************************";
        };
    };
    ensTlds?: readonly string[] | undefined;
    id: 80008;
    name: "Polynomia Sepolia";
    nativeCurrency: {
        readonly decimals: 18;
        readonly name: "<PERSON>ther";
        readonly symbol: "ETH";
    };
    rpcUrls: {
        readonly default: {
            readonly http: readonly ["https://rpc.sepolia.polynomial.fi"];
        };
    };
    sourceId?: number | undefined | undefined;
    testnet: true;
    custom?: Record<string, unknown> | undefined;
    fees?: import("../../index.js").ChainFees<undefined> | undefined;
    formatters?: undefined;
    serializers?: import("../../index.js").ChainSerializers<undefined, import("../../index.js").TransactionSerializable> | undefined;
};
//# sourceMappingURL=polynomialSepolia.d.ts.map