<?php


namespace app\common\model;


use think\Model;

class MiningRecord extends Model
{
    protected $table = 'stack_record';

    const Type_Income = 1;  //  收益余额质押
    const Type_Wallet = 2;  //  钱包余额质押

    protected $append = [
        'status',
        'buy_time_text',
        'end_time_text',
        'type_text',
        'buy_status_text',
    ];

    public function getStatusAttr($value,$data){
        if (!empty($data['end_time'])){
            return $data['end_time'] > time() ? 1 : 0;
        }
        return 1;
    }

    public function getTypeTextAttr($value,$data){
        return empty($data['type']) ? '' : ($data['type'] == 1 ? '收益余额' : '钱包余额');
    }
    public function getBuyStatusTextAttr($value,$data){
        return isset($data['buy_status']) ? ($data['buy_status'] == 1 ? '已完成' : '未完成') : '';
    }

    public function getBuyTimeTextAttr($value,$data){
        return empty($data['buy_time']) ? '' : date('Y-m-d H:i:s',$data['buy_time']);
    }

    public function getEndTimeTextAttr($value,$data){
        return empty($data['end_time']) ? '' : date('Y-m-d H:i:s',$data['end_time']);
    }

    public function mining(){
        return $this->hasOne('Mining','id','mining_id')->setEagerlyType(0);
    }

    public function fish(){
        return $this->hasOne(Fish::class,'id','fish_id')->setEagerlyType(0);
//        return $this->belongsToMany(Fish::class,'fish','fish_id','id');
    }
}
