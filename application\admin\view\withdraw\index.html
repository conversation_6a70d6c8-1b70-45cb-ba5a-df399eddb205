{extend name="public:template" /}

{block name="content"}
<h2 class="section-title">提现管理</h2>
<div style="text-align: center;font-size: 23px;color: red"></div>

 <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <div class="card-header-form ml-auto">
                        <a href="{:url('Withdraw/detail')}" class="btn btn-icon icon-left btn-primary"><i class="far fa-edit"></i> 创建</a>
                    </div>
                </div>

                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-md">
                            <tr>
                                <th>编号</th>
                                <th>用户</th>
                                <th>提现地址</th>
                                <th>金额</th>
<!--                                <th>类型</th>-->
                                <th>状态</th>
                                <th>备注</th>
                                <th>创建时间</th>
                                <th>更新时间</th>
                                <th class="text-center">操作</th>
                            </tr>
                            {volist name="data" id="vo" key="key"}
                            <tr>
                                <td>{$vo.id}</td>
                                <td>{$vo.fish.address}</td>
                                <td>{$vo.wd_address}</td>
                                <td>{$vo.amount}</td>
<!--                                <td>{$vo.type}</td>-->

                                <td>{$vo.status_text}</td>
                                <td>{$vo.remark}</td>
                                <td>{$vo.create_time}</td>
                                <td>{$vo.update_time}</td>
                                <td>
                                    {if $role == 1}
                                    <a href="{:url('Withdraw/detail', ['id' => $vo.id])}" class="btn btn-icon icon-left btn-primary mr-3"><i class="far fa-edit"></i> 审核</a>
                                    {/if}
<!--                                    <a href="javascript:;" class="btn btn-icon icon-left btn-danger" onclick="delImage('{$vo.id}')"><i class="fas fa-times"></i> 删除</a>-->
                                </td>
                            </tr>
                            {/volist}
                        </table>
                    </div>
                </div>

                <div class="card-footer text-right">
                    <nav class="d-inline-block">
                        {$data|raw}
                    </nav>
                </div>
            </div>
        </div>
    </div>
{/block}


{block name="extend-script"}
<script type="text/javascript">
    function enable(id) {
        $.ajax({
            url: "{:url('Withdraw/enable')}",
            method: 'post',
            data: {id: id},
            dataType: 'json',
            success(res) {
                if (res.success === true) {
                    swal('操作成功', {buttons: false, icon: 'success'});
                    setTimeout(function () { location.reload() }, 1500)
                }
                if (res.success === false) swal('出现错误', res.err_msg, 'error');
            }
        })
    }

    function delImage(id) {
        swal({
            title: '确定删除该数据？',
            icon: 'warning',
            buttons: ['取消', '确认'],
            dangerMode: true,
        })
            .then((willDelete) => {
                if (! willDelete)
                    return;

                $.ajax({
                    url: "{:url('Withdraw/destroy')}",
                    method: 'post',
                    data: {id: id},
                    dataType: 'json',
                    success(res) {
                        if (res.success === true) {
                            swal('操作成功', {buttons: false, icon: 'success'});
                            setTimeout(function () { location.reload() }, 1500)
                        }
                        if (res.success === false) swal('出现错误', res.err_msg, 'error');
                    }
                })
            });
    }
</script>
{/block}
