/**
 * Network Configuration for Multi-Chain Support
 * XMKF Digital Asset Platform
 */

// Import ethers for network configuration
// Note: This will be loaded via CDN or npm in the actual implementation

const NetworkConfig = {
    // Ethereum Mainnet
    ethereum: {
        chainId: 1,
        name: 'Ethereum',
        currency: 'ETH',
        explorerUrl: 'https://etherscan.io',
        rpcUrl: 'https://mainnet.infura.io/v3/YOUR_INFURA_KEY',
        contracts: {
            USDT: '******************************************',
            USDC: '******************************************'
        },
        tokens: {
            USDT: {
                address: '******************************************',
                decimals: 6,
                symbol: 'USDT',
                name: 'Tether USD'
            },
            USDC: {
                address: '******************************************',
                decimals: 6,
                symbol: 'USDC',
                name: 'USD Coin'
            }
        }
    },

    // Sepolia Testnet
    sepolia: {
        chainId: 11155111,
        name: 'Sepolia',
        currency: 'ETH',
        explorerUrl: 'https://sepolia.etherscan.io',
        rpcUrl: 'https://sepolia.infura.io/v3/YOUR_INFURA_KEY',
        contracts: {
            USDT: '0x...',  // Test contract addresses
            USDC: '0x...'
        },
        tokens: {
            USDT: {
                address: '0x...',
                decimals: 6,
                symbol: 'USDT',
                name: 'Test Tether USD'
            }
        }
    },

    // Binance Smart Chain
    bsc: {
        chainId: 56,
        name: 'BSC',
        currency: 'BNB',
        explorerUrl: 'https://bscscan.com',
        rpcUrl: 'https://bsc-dataseed.binance.org',
        contracts: {
            USDT: '******************************************',
            BUSD: '******************************************'
        },
        tokens: {
            USDT: {
                address: '******************************************',
                decimals: 18,
                symbol: 'USDT',
                name: 'Tether USD'
            },
            BUSD: {
                address: '******************************************',
                decimals: 18,
                symbol: 'BUSD',
                name: 'Binance USD'
            }
        }
    },

    // Polygon
    polygon: {
        chainId: 137,
        name: 'Polygon',
        currency: 'MATIC',
        explorerUrl: 'https://polygonscan.com',
        rpcUrl: 'https://polygon-rpc.com',
        contracts: {
            USDT: '******************************************',
            USDC: '******************************************'
        },
        tokens: {
            USDT: {
                address: '******************************************',
                decimals: 6,
                symbol: 'USDT',
                name: 'Tether USD'
            },
            USDC: {
                address: '******************************************',
                decimals: 6,
                symbol: 'USDC',
                name: 'USD Coin'
            }
        }
    }
};

// Network switching utilities
const NetworkUtils = {
    // Get network by chain ID
    getNetworkByChainId(chainId) {
        return Object.values(NetworkConfig).find(network => network.chainId === chainId);
    },

    // Get supported chain IDs
    getSupportedChainIds() {
        return Object.values(NetworkConfig).map(network => network.chainId);
    },

    // Check if chain is supported
    isChainSupported(chainId) {
        return this.getSupportedChainIds().includes(chainId);
    },

    // Get network name by chain ID
    getNetworkName(chainId) {
        const network = this.getNetworkByChainId(chainId);
        return network ? network.name : 'Unknown Network';
    },

    // Get token info by network and symbol
    getTokenInfo(networkKey, tokenSymbol) {
        const network = NetworkConfig[networkKey];
        return network && network.tokens ? network.tokens[tokenSymbol] : null;
    }
};

// Export for global use
window.NetworkConfig = NetworkConfig;
window.NetworkUtils = NetworkUtils;

console.log('✓ Network Configuration loaded');
