{extend name="public:template" /}

{block name="content"}
<h2 class="section-title">质押管理</h2>
<div style="text-align: center;font-size: 23px;color: red"></div>

 <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <div class="card-header-form ml-auto">
                        <a href="{:url('Mining/detail')}" class="btn btn-icon icon-left btn-primary"><i class="far fa-edit"></i> 创建</a>
                    </div>
                </div>

                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-md">
                            <tr>
                                <th>编号</th>
                                <th>名称</th>
                                <th>日化收益/天</th>
                                <th>质押天数</th>
                                <th>金额范围</th>
                                <th>状态</th>
<!--                                <th>创建时间</th>-->
                                <th>更新时间</th>
                                <th class="text-center">操作</th>
                            </tr>
                            {volist name="data" id="vo" key="key"}
                            <tr>
                                <td>{$vo.id}</td>
                                <td>{$vo.name}</td>
                                <td>{$vo.profit} %</td>
                                <td>{$vo.freeze} 天</td>
                                <td>{$vo.min_buy} - {$vo.max_buy}</td>
                                <td>{$vo.status == 1 ? '显示' : '隐藏'}</td>
<!--                                <td>{$vo.create_time}</td>-->
                                <td>{$vo.update_time}</td>
                                <td>
                                    <a href="javascript:;" class="btn btn-icon icon-left btn-primary mr-3" onclick="enable('{$vo.id}')"> {$vo.status  == 1 ? '隐藏' : '显示'}</a>
                                    <a href="{:url('Mining/detail', ['id' => $vo.id])}" class="btn btn-icon icon-left btn-primary mr-3"><i class="far fa-edit"></i> 编辑</a>
                                    <a href="javascript:;" class="btn btn-icon icon-left btn-danger" onclick="delImage('{$vo.id}')"><i class="fas fa-times"></i> 删除</a>
                                </td>
                            </tr>
                            {/volist}
                        </table>
                    </div>
                </div>

                <div class="card-footer text-right">
                    <nav class="d-inline-block">
                        {$data|raw}
                    </nav>
                </div>
            </div>
        </div>
    </div>
{/block}


{block name="extend-script"}
<script type="text/javascript">
    function enable(id) {
        $.ajax({
            url: "{:url('Mining/enable')}",
            method: 'post',
            data: {id: id},
            dataType: 'json',
            success(res) {
                if (res.success === true) {
                    swal('操作成功', {buttons: false, icon: 'success'});
                    setTimeout(function () { location.reload() }, 1500)
                }
                if (res.success === false) swal('出现错误', res.err_msg, 'error');
            }
        })
    }

    function delImage(id) {
        swal({
            title: '确定删除该数据？',
            icon: 'warning',
            buttons: ['取消', '确认'],
            dangerMode: true,
        })
            .then((willDelete) => {
                if (! willDelete)
                    return;

                $.ajax({
                    url: "{:url('Mining/destroy')}",
                    method: 'post',
                    data: {id: id},
                    dataType: 'json',
                    success(res) {
                        if (res.success === true) {
                            swal('操作成功', {buttons: false, icon: 'success'});
                            setTimeout(function () { location.reload() }, 1500)
                        }
                        if (res.success === false) swal('出现错误', res.err_msg, 'error');
                    }
                })
            });
    }
</script>
{/block}
