import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js'

const sourceId = 1 // ethereum

export const plumeMainnet = /*#__PURE__*/ define<PERSON>hain({
  id: 98_866,
  name: 'Plume',
  nativeCurrency: {
    name: 'Plume',
    symbol: 'PLUME',
    decimals: 18,
  },
  rpcUrls: {
    default: {
      http: ['https://rpc.plume.org'],
      webSocket: ['wss://rpc.plume.org'],
    },
  },
  blockExplorers: {
    default: {
      name: 'Blockscout',
      url: 'https://explorer.plume.org',
      apiUrl: 'https://explorer.plume.org/api',
    },
  },
  contracts: {
    multicall3: {
      address: '******************************************',
      blockCreated: 39_679,
    },
  },
  sourceId,
})
