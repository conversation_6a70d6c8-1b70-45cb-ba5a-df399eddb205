{"name": "@reown/appkit-scaffold-ui", "version": "1.7.11", "type": "module", "main": "./dist/esm/exports/index.js", "types": "./dist/types/exports/index.d.ts", "files": ["dist", "!tsconfig.tsbuildinfo", "README.md"], "exports": {".": {"types": "./dist/types/exports/index.d.ts", "import": "./dist/esm/exports/index.js", "default": "./dist/esm/exports/index.js"}, "./w3m-modal": {"types": "./dist/types/exports/w3m-modal.d.ts", "import": "./dist/esm/exports/w3m-modal.js", "default": "./dist/esm/exports/w3m-modal.js"}, "./utils": {"types": "./dist/types/exports/utils.d.ts", "import": "./dist/esm/exports/utils.js", "default": "./dist/esm/exports/utils.js"}, "./core": {"types": "./dist/types/exports/core.d.ts", "import": "./dist/esm/exports/core.js", "default": "./dist/esm/exports/core.js"}, "./basic": {"types": "./dist/types/exports/basic.d.ts", "import": "./dist/esm/exports/basic.js", "default": "./dist/esm/exports/basic.js"}, "./swaps": {"types": "./dist/types/exports/swaps.d.ts", "import": "./dist/esm/exports/swaps.js", "default": "./dist/esm/exports/swaps.js"}, "./send": {"types": "./dist/types/exports/send.d.ts", "import": "./dist/esm/exports/send.js", "default": "./dist/esm/exports/send.js"}, "./receive": {"types": "./dist/types/exports/receive.d.ts", "import": "./dist/esm/exports/receive.js", "default": "./dist/esm/exports/receive.js"}, "./onramp": {"types": "./dist/types/exports/onramp.d.ts", "import": "./dist/esm/exports/onramp.js", "default": "./dist/esm/exports/onramp.js"}, "./transactions": {"types": "./dist/types/exports/transactions.d.ts", "import": "./dist/esm/exports/transactions.js", "default": "./dist/esm/exports/transactions.js"}, "./embedded-wallet": {"types": "./dist/types/exports/embedded-wallet.d.ts", "import": "./dist/esm/exports/embedded-wallet.js", "default": "./dist/esm/exports/embedded-wallet.js"}, "./socials": {"types": "./dist/types/exports/socials.d.ts", "import": "./dist/esm/exports/socials.js", "default": "./dist/esm/exports/socials.js"}, "./email": {"types": "./dist/types/exports/email.d.ts", "import": "./dist/esm/exports/email.js", "default": "./dist/esm/exports/email.js"}, "./pay": {"types": "./dist/types/exports/pay.d.ts", "import": "./dist/esm/exports/pay.js", "default": "./dist/esm/exports/pay.js"}}, "dependencies": {"lit": "3.3.0", "@reown/appkit-common": "1.7.11", "@reown/appkit-controllers": "1.7.11", "@reown/appkit-ui": "1.7.11", "@reown/appkit-utils": "1.7.11", "@reown/appkit-wallet": "1.7.11"}, "author": "Reown (https://discord.gg/reown)", "license": "Apache-2.0", "homepage": "https://github.com/reown-com/appkit", "repository": {"type": "git", "url": "git+https://github.com/reown-com/appkit.git"}, "bugs": {"url": "https://github.com/reown-com/appkit/issues"}, "devDependencies": {"@open-wc/testing": "4.0.0", "vitest": "3.1.3", "@vitest/coverage-v8": "2.1.9"}, "keywords": ["appkit", "wallet", "onboarding", "reown", "dapps", "web3", "wagmi", "ethereum", "solana", "bitcoin"], "scripts": {"build:clean": "rm -rf dist", "build": "tsc --build", "watch": "tsc --watch", "typecheck": "tsc --noEmit", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "test": "vitest run --coverage.enabled=true -- coverage.reporter=json --coverage.reporter=json-summary --coverage.reportOnFailure=true"}}