import { define<PERSON>hain } from '../../utils/chain/defineChain.js'

export const shiden = /*#__PURE__*/ defineChain({
  id: 336,
  name: '<PERSON><PERSON>',
  nativeCurrency: {
    decimals: 18,
    name: '<PERSON><PERSON>',
    symbol: 'SDN',
  },
  rpcUrls: {
    default: {
      http: ['https://shiden.public.blastapi.io'],
      webSocket: ['wss://shiden-rpc.dwellir.com'],
    },
  },
  blockExplorers: {
    default: {
      name: '<PERSON><PERSON>an',
      url: 'https://shiden.subscan.io',
    },
  },
  testnet: false,
})
