/**
 * Reown AppKit Configuration
 * XMKF Digital Asset Platform - Web3 Wallet Integration
 */

// Reown Cloud Project ID - XMKF Digital Asset Platform
const PROJECT_ID = '1a54ba92caa7810745990910f7daccc4';

// Network configurations for multi-chain support
const NETWORKS = {
    ethereum: {
        chainId: 1,
        name: 'Ethereum',
        currency: 'ETH',
        explorerUrl: 'https://etherscan.io',
        rpcUrl: 'https://mainnet.infura.io/v3/YOUR_INFURA_KEY'
    },
    sepolia: {
        chainId: 11155111,
        name: '<PERSON><PERSON>',
        currency: 'ETH',
        explorerUrl: 'https://sepolia.etherscan.io',
        rpcUrl: 'https://sepolia.infura.io/v3/YOUR_INFURA_KEY'
    },
    bsc: {
        chainId: 56,
        name: 'BSC',
        currency: 'BNB',
        explorerUrl: 'https://bscscan.com',
        rpcUrl: 'https://bsc-dataseed.binance.org'
    },
    polygon: {
        chainId: 137,
        name: 'Polygon',
        currency: 'MATIC',
        explorerUrl: 'https://polygonscan.com',
        rpcUrl: 'https://polygon-rpc.com'
    }
};

// Supported wallets configuration
const WALLET_CONFIG = {
    recommended: ['metamask', 'walletConnect', 'coinbase'],
    social: ['google', 'discord', 'x'],
    mobile: ['trust', 'rainbow', 'zerion']
};

// AppKit theme configuration
const THEME_CONFIG = {
    themeMode: 'light',
    themeVariables: {
        '--w3m-font-family': 'Arial, sans-serif',
        '--w3m-accent': '#8e5729',
        '--w3m-color-mix': '#8e5729',
        '--w3m-color-mix-strength': 20,
        '--w3m-border-radius-master': '8px'
    }
};

// Export configuration for use in other modules
window.APPKIT_CONFIG = {
    PROJECT_ID,
    NETWORKS,
    WALLET_CONFIG,
    THEME_CONFIG
};

console.log('✓ AppKit Configuration loaded');
