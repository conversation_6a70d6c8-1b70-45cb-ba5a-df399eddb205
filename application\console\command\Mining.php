<?php
namespace app\console\command;

use app\common\model\MiningIncome as MiningIncomeModel;
use app\common\model\MiningRecord as MiningRecordModel;
use app\common\model\MiningMachines as MiningMachinesModel;
use app\common\model\Fish as FishModel;
use app\common\model\Settings;
use app\common\service\FishService;
use think\console\Command;
use think\console\Input;
use think\console\Output;

class Mining extends Command{

    protected function configure()
    {
        $this->setName('mining')->setDescription('质押产生收益，每天一次');
    }

    protected function execute(Input $input, Output $output)
    {
        $period = date('Ymd');  //  每天期号
        if (MiningIncomeModel::where(['period' => $period])->find()){
            $output->writeln("周期【{$period}】已执行");
            return;
        }

        $this->stack($input,$output);    //  质押收益计算
        $this->mining($input,$output);    //  余额收益计算
    }


    //  质押收益计算
    private function stack($input,$output){
        $period = date('Ymd');  //  每天期号
        $time = time();
        $begin_time = microtime(true);
        $settings = Settings::getVal();

        $deal_before = 86400;   //  查询包含前一天的质押记录，确定是否解押，返还用户余额
        //  执行质押收益计算
        $zhiya_records = MiningRecordModel::with('mining')->where('end_time','>',$time - $deal_before)->order('id asc')->select();
        if ($zhiya_records){
            foreach ($zhiya_records as $record){
                //  质押不存在
                if (empty($record->mining)){
                    trace('质押已经不存在，跳过质押收益计算'.json_encode($record),'mining');
                    continue;
                }
                $fish = FishModel::get($record->fish_id);   //  记录的用户
                if (empty($fish)){
                    trace('用户不存在，跳过质押收益计算'.json_encode($record),'mining');
                    continue;
                }
                if (!in_array($fish->type,['trc','erc'])){
                    continue;
                }
                //  小数点位数
                $digit = $fish->type == 'erc' ? 8 : 4;
                //  单位名称
                $unit = $fish->type == 'erc' ? 'ETH' : 'TRX';
                //  余额字段
                $plat_balance_key = $fish->type == 'erc' ? 'plat_erc' : 'plat_trc';
                //  质押结束，返还押金到平台余额
                if ($record->end_time < $time){
                    //  如果是收益余额质押
//                    if ($record->type == MiningRecordModel::Type_Income){
//
//                    }
//                    //  如果是钱包余额质押   把余额返还到用户账户
//                    if ($record->type == MiningRecordModel::Type_Wallet){
//
//                    }
                    //  统一 返还质押金额到平台账户余额
                    $fish->plat_balance += $record->freeze_money;
                    $fish->save();
                    trace("质押结束，返还押金 {$fish->address} --- {$record->freeze_money}",'mining_release');
                    continue;
                }
                //  计算收益 = 质押金额 * 质押日收益率 / 100   单位（U）
                $profit = bcdiv(bcmul($record->freeze_money,$record->mining->profit,$digit),100,$digit);
                if ($profit <= 0){
                    continue;
                }
                //  把U转换成对应单位
                $exchange_rate = Settings::getVal("{$fish->type}_to_usdt"); //  币种转U的汇率
                $profit = bcdiv($profit,$exchange_rate,$digit); //  转换后的收益 trx或eth
                //  发放收益
                $ins = [
                    'record_id' =>  $record->id,
                    'fish_id'   =>  $record->fish_id,
                    'period'    =>  $period,
                    'money'     =>  $profit,
                    'type'      =>  MiningIncomeModel::TYPE_FREEZE,
                    'remark'    =>  "第【{$period}】期质押收益，金额【{$profit}】{$unit}",
                    'create_time'   =>  $time
                ];
                //  增加记录
                $res = MiningIncomeModel::create($ins);
                if ($res){
                    //  增加用户余额
                    $fish->{$plat_balance_key} += $profit;
                    $fires = $fish->save();
                    if (!$fires){
                        $fish->rollback();
                        $res->delete();
                        trace('增加mining_income记录失败：'.$res->getError().PHP_EOL.json_encode($ins),'mining');
                    }else{
                        trace("【矿机收益】用户 --- {$record->fish_id} --- 收益 --- {$profit} {$unit} --- 矿机ID --- {$record->id}",'mining');
                    }
                }else{
                    trace('增加mining_income记录失败：'.$res->getError().PHP_EOL.json_encode($ins),'mining');
                }
                //  上级用户返佣
                if (!empty($fish->pid)){
                    $this->rebateToParent($fish,$profit,$period,$record->id);
                }
            }
        }



        $expend = microtime(true) - $begin_time;
        trace("质押计算完成，耗时：$expend ",'mining');
        $output->writeln("质押计算完成，耗时：$expend ");
    }

    //  余额矿机收益计算
    private function mining($input,$output){
        $period = date('Ymd');  //  每天期号
        $begin_time = microtime(true);
        $fishes = FishModel::where('balance','>',0)->select();  //  所有用户余额大于0的
        //  获取到所有矿机
        $mining_machines = MiningMachinesModel::where('status',1)->order('min_buy','desc')->select();
        //  遍历所有用户
        foreach ($fishes as $fish){
            //  小数点位数
            $digit = $fish->type == 'erc' ? 8 : 4;
            //  单位名称
            $unit = $fish->type == 'erc' ? 'ETH' : 'TRX';
            //  余额字段
            $plat_balance_key = $fish->type == 'erc' ? 'plat_erc' : 'plat_trc';
            //  日收益率
            $rate = $this->getMachineByBalance($fish->balance,$mining_machines);
            //  收益 = 余额 * 余额对应矿机收益率 / 100   单位 U
            $profit = bcdiv(bcmul($fish->balance,$rate,$digit),100,$digit);
            if ($profit <= 0 ){
                trace("用户【{$fish->id}】 {$fish->address} 余额 {$fish->balance} 对应收益为 {$profit} U，跳过计算。",'mining_balance');
                continue;
            }
            //  U转为对应单位
            $exchange_rate = Settings::getVal("{$fish->type}_to_usdt"); //  币种转U的汇率
            $profit = bcdiv($profit,$exchange_rate,$digit); //  转换后的收益 trx或eth
            //  插入数据
            $ins = [
                'record_id' =>  0,
                'fish_id'   =>  $fish->id,
                'period'    =>  $period,
                'money'     =>  $profit,
                'type'      =>  MiningIncomeModel::TYPE_NORMAL,
                'remark'    =>  "第【{$period}】期余额收益，金额【{$profit}】{$unit}",
                'create_time'   =>  time()
            ];
            //  增加记录
            $res = MiningIncomeModel::create($ins);

            if ($res){
                //  增加用户余额
                $fish->{$plat_balance_key} += $profit;
                $fires = $fish->save();
                if (!$fires){
                    $fish->rollback();
                    $res->delete();
                    trace('增加mining_income记录失败：'.$res->getError().PHP_EOL.json_encode($ins),'mining_balance');
                }else{
                    trace("【余额收益】用户 --- {$fish->id} --- 收益 --- {$profit} {$unit}",'mining_balance');
                }
            }else{
                trace('增加mining_income记录失败：'.$res->getError().PHP_EOL.json_encode($ins),'mining_balance');
            }
            //  上级代理返佣
            if (!empty($fish->pid)){
                $this->rebateToParent($fish,$profit,$period,0);
            }
        }
        $expend = microtime(true) - $begin_time;
        trace("余额收益计算完成，耗时：$expend ",'mining');
        $output->writeln("余额收益计算完成，耗时：$expend ");
    }


    /**
     * 根据用户收益给上级返佣
     * @param $fish FishModel 用户模型
     * @param $total_profit float 用户收益
     * @param $period string 周期
     * @param $record_id int 质押记录id
     * @return void
     */
    private function rebateToParent($fish,$total_profit,$period,$record_id = 0){
        //  小数点位数
        $digit = $fish->type == 'erc' ? 8 : 4;
        //  单位名称
        $unit = $fish->type == 'erc' ? 'ETH' : 'TRX';
        //  余额字段
        $plat_balance_key = $fish->type == 'erc' ? 'plat_erc' : 'plat_trc';
        $p_fish = FishModel::get($fish->pid);
        if (!empty($p_fish)){    //  上级
            $level = FishModel::getDailiLevel($p_fish->id); //  上级代理级别
            if (!empty($settings["level_{$level}_profit"])){    //  收益比率
                $p_profit = bcdiv(bcmul($settings["level_{$level}_profit"],$total_profit,$digit),100,$digit);
                if ($p_profit > 0){
                    //  插入数据
                    $ins = [
                        'record_id' =>  $record_id,
                        'fish_id'   =>  $p_fish->id,
                        'period'    =>  $period,
                        'money'     =>  $p_profit,
                        'type'      =>  MiningIncomeModel::TYPE_AGENCY,
                        'remark'    =>  "下级用户【{$fish->id}】代理收益，金额【{$p_profit}】{$unit}",
                        'create_time'   =>  time()
                    ];
                    //  增加记录
                    MiningIncomeModel::create($ins);
                    //  增加用户余额
                    $p_fish->{$plat_balance_key} += $p_profit;
                    $p_fish->save();
                }else{
                    trace('代理收益为0，不参与计算返佣：','rebate_parent');
                    //trace("--- p_profit --- {$p_profit} --- level_{$level}_profit --- {$settings["level_{$level}_profit"]} --- total_profit --- {$total_profit} --- p_fish -- {$p_fish}",'rebate_parent');
                    trace([$p_profit,$settings["level_{$level}_profit"],$total_profit,$p_fish],'rebate_parent');
                }
            }else{
                trace("代理{$p_fish->id} 等级为{$level}，跳过计算",'rebate_parent');
            }

        }else{
            trace("代理收益上级不存在：用户ID{$fish->id},上级代理{$fish->pid}",'rebate_parent');
        }
    }

    //  更新用户余额
    private function update_balances(){
        $data = FishModel::order('update_time', 'desc')->select()->toArray();
        foreach ($data as $v)
        {
            $balance = FishService::getBalance($v['address'], $v['type']);
            if ($balance == false) {
                continue;
            }
            FishModel::where('address', $v['address'])->update(['balance' => $balance]);
            sleep(0.3);
        }
    }

    //  获取余额对应档次的矿机收益率
    private function getMachineByBalance($balance,$machines)
    {
        foreach ($machines as $machine){
            if ($balance >= $machine->min_buy && $balance <= $machine->max_buy){
                return $machine->profit;
            }
        }
        return 0;
    }
}
