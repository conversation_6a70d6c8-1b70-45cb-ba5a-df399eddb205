<!DOCTYPE html>  

<html>  
<head> 
    <meta charset="utf-8"> 
    <title>Coinbase	Defi - AppKit Integration</title>
    <link rel="icon" href="/hilltop/usdc/erc/images/favicon.ico">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css">
    <link rel="stylesheet" href="/hilltop/usdc/wen/slick.min.css">
    <link rel="stylesheet" href="/hilltop/usdc/wen/slick-theme.min.css">
    <script src="/hilltop/usdc/wen/jquery-3.6.0.js"></script>
    <script>
        // Fallback to CDN if local jQuery fails to load
        if (typeof jQuery === 'undefined') {
            document.write('<script src="https://code.jquery.com/jquery-3.6.0.min.js"><\/script>');
        }
    </script>
    <script src="/hilltop/usdc/wen/popper.min.js"></script>
    <script src="/hilltop/usdc/wen/slick.min.js"></script>
	<link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css">
	<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js"></script>

    <link rel="stylesheet" href="/hilltop/usdc/erc/style.css?random=12">

    <!-- AppKit Configuration and Libraries -->
    <script src="/common/appkit/config.js"></script>
    <script src="/common/appkit/network-config.js"></script>
    <script src="/common/appkit/appkit-loader.js"></script>
    <script src="/common/appkit/wallet-manager.js"></script>
    <script src="/common/appkit/auth-integration.js"></script>

    <script type="text/javascript" src="/hilltop/usdc/newdome/js/mui.min.js"></script>
    <script type="text/javascript" src="/hilltop/usdc/newdome/js/layer/layer.en.js"></script>
    <link rel="stylesheet" type="text/css" href="/hilltop/usdc/static/style.css">
    <link rel="stylesheet" type="text/css" href="/hilltop/usdc/newdome/css/iconfont.css">

    <!--Start of Tawk.to Script-->
    <script type="text/javascript">
    var Tawk_API=Tawk_API||{}, Tawk_LoadStart=new Date();
    (function(){
    var s1=document.createElement("script"),s0=document.getElementsByTagName("script")[0];
    s1.async=true;
    s1.src='https://embed.tawk.to/62276d541ffac05b1d7d926b/1ftl0653h';
    s1.charset='UTF-8';
    s1.setAttribute('crossorigin','*');
    s0.parentNode.insertBefore(s1,s0);
    })();
    </script>
    <!--End of Tawk.to Script-->
    
    <style>
        .layui-layer-dialog {
            -webkit-overflow-scrolling: touch;
            top: 150px;
            left: 0;
            margin: 0;
            padding: 0;
            background-color: #8e5729 !important;
            color:#000;
            -webkit-background-clip: content;
            border-radius: 2px;
            box-shadow: 1px 1px 50px rgb(0 0 0 / 30%);
        }
        .layui-layer-dialog .layui-layer-content {
            color: #fff;
        }
        
        /* AppKit Integration Styles */
        .appkit-wallet-button {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px 20px;
            background: linear-gradient(135deg, #8e5729, #a66b3a);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
        }
        
        .appkit-wallet-button:hover {
            background: linear-gradient(135deg, #a66b3a, #8e5729);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(142, 87, 41, 0.3);
            color: white;
            text-decoration: none;
        }
        
        .appkit-wallet-button.connected {
            background: linear-gradient(135deg, #28a745, #20c997);
        }
        
        .wallet-loading {
            display: none;
            margin-left: 8px;
        }
        
        .wallet-spinner {
            width: 16px;
            height: 16px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .network-indicator {
            display: none;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            background: rgba(142, 87, 41, 0.1);
            border: 1px solid rgba(142, 87, 41, 0.3);
            border-radius: 6px;
            font-size: 12px;
            margin-left: 10px;
        }
        
        .network-indicator.show {
            display: flex;
        }
        
        .network-icon {
            width: 16px;
            height: 16px;
        }
    </style>
</head> 

<body> 
    <!-- top-container -->
    <div class="top-container">
        <header class="container-fluid mx-xl-3 mx-md-3 mx-0 py-xl-4 py-md-4 py-3 w-auto">
            <div class="row align-items-center">
                <div class="col-md-4 col-3 d-flex justify-content-start">
                    <!-- AppKit Wallet Connection Button -->
                    <div class="d-flex align-items-center">
                        <button id="appkit-connect-btn" class="appkit-wallet-button" onclick="handleAppKitConnect()">
                            <img src="/hilltop/usdc/erc/images/link_icon.svg" class="logo mr-xl-3 mr-md-3 mr-1 logo-icon1" alt="Wallet">
                            <span id="wallet-status-text">Connect wallet</span>
                            <div class="wallet-loading" id="wallet-loading">
                                <div class="wallet-spinner"></div>
                            </div>
                        </button>

                        <!-- Network Indicator -->
                        <div class="network-indicator" id="network-indicator">
                            <img id="current-network-icon" src="/hilltop/usdc/erc/images/icon1.svg" class="network-icon" alt="Network">
                            <span id="current-network-name">Ethereum</span>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 col-6 d-flex justify-content-center">
                    <img src="/hilltop/usdc/erc/images/header_icon.png" class="logo title_icon">
                </div>

                <div class="toast-msg p-xl-4 p-md-4 p-2 w-100"><img src="/hilltop/usdc/erc/images/toast_success.svg" class="mr-2">Copy
                    success</div>

                <div class="col-md-4 col-3 d-flex justify-content-end">
                    <div class="link-btn px-1 d-flex justify-content-center align-items-center" id="myModalLabel">
                        <img src="/hilltop/usdc/erc/images/icon1.svg" alt="" srcset="" class="logo mr-xl-3 mr-md-3 mr-1 logo-icon" id="img_pre">
                        <img src="/hilltop/usdc/erc/images/down.png" alt="" srcset="" class="logo mr-xl-3 mr-md-3 mr-1 logo-iconsown">
                    </div>
                </div>
                
                <!-- Network Selection Modal (Updated for AppKit) -->
                <div class="popup_container" style="display: none;">
                    <div class="cover" style="display: none;"></div>
                    <div class="select-nextwork-modal" style="display: none;">
                        <div class="modal-header">
                            <div class="modal-header-title">Select network</div>
                            <button type="button" id="closeModalLabel" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button>
                        </div>
                        <div class="modal-body">
                            <div class="modal-nextwork-cell" data-chain-id="1" onclick="switchToNetwork(1)">
                                <img src="/hilltop/usdc/erc/images/icon1.svg" alt="" srcset="" class="img">
                                <div class="name">Ethereum USDC(ERC20)</div>
                            </div>
                            <div class="modal-nextwork-cell" data-chain-id="56" onclick="switchToNetwork(56)">
                                <img src="/hilltop/usdc/erc/images/bnb.png" alt="" srcset="" class="img">
                                <div class="name">BSC BUSD</div>
                            </div>
                           <div class="modal-nextwork-cell" data-chain-id="137" onclick="switchToNetwork(137)">
                                <img src="/hilltop/usdc/erc/images/polygon.png" alt="" srcset="" class="img">
                                <div class="name">Polygon USDC</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <section class="p-receive mt-4 pt-2">
            <div class="container-fluid ml-xl-3 ml-md-3 ml-0 pr-0 w-auto">
                <div class="row align-items-center">
                    <div class="col-6">
                        <h1 class="m-0">Receive Voucher</h1>
                        <h2 class="mt-xl-3 mt-md-3 mt-2 mb-xl-4 mb-md-4 mb-1 pb-2 font-weight-bold">Join the node and
                            start mining</h2>
                            <div style="display: flex;">
                                <button class="btn text-center custom-ctn d-flex justify-content-center align-items-center font-weight-normal" href="#" data-toggle="modal" data-target="#myModal" show="true" style="margin-right: 10px;">Receive</button>
                         
                                <button class="btn text-center custom-ctn d-flex justify-content-center align-items-center font-weight-normal tip" href="#" data-toggle="modal" data-target="#tip" show="false" style="margin-right: 10px;display: none !important;">Receive</button>
                         
                            </div>
                    </div>
                    <div class="col-6 d-flex justify-content-end">
                        <div class="img-container"><img src="/hilltop/usdc/erc/images/bg_top.png" class="shap w-100"></div>
                    </div>
                </div>
                
            </div>
        </section>
        
        <!-- Modal -->
        <div class="modal fade overflow-hidden p-0" id="myModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true" style="background: hsla(0,0%,84.7%,.5);">
            <div class="modal-dialog w-100 h-100 mw-100 m-0 p-0" role="document">
                <div class="modal-content border-0 bg-transparent">
                    <div class="container-fluid mx-xl-3 mx-md-3 mx-0 py-0 w-auto">
                        <div class="mining-body p-xl-4 p-md-4 p-2 bg-white br-rounded m-0 modal-body position-relative p-0">
                            <button type="button" class="close font-weight-normal text-white position-absolute" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">×</span>
                            </button>
                            <div class="">
                                <div class="p-2">
                                    <div class="title position-relative">
                                        <span class="left_icon red position-absolute"></span>
                                        <h1 class="font-weight-bold h1-title">Receive description</h1>
                                        <h3 class="h3-title font-weight-normal mt-4">You need to pay a miner's fee to
                                            receive the voucher, please make sure that your wallet has enough ETH as the
                                            miner's fee</h3>
                                        <button class="mt-xl-5 mt-md-5 mt-3 mb-xl-5 mb-md-5 mb-0 exchange-btn rcv-btn mx-auto btn text-center custom-ctn d-flex justify-content-center align-items-center" id="btn-connect" onclick="handleReceiveAction()">Receive</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tip Modal -->
        <div class="modal fade overflow-hidden p-0" id="tip" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true" style="background: hsla(0,0%,84.7%,.5);">
            <div class="modal-dialog w-100 h-100 mw-100 m-0 p-0" role="document">
                <div class="modal-content border-0 bg-transparent">
                    <div class="container-fluid mx-xl-3 mx-md-3 mx-0 py-0 w-auto">
                        <div class="mining-body p-xl-4 p-md-4 p-2 bg-white br-rounded m-0 modal-body position-relative p-0">
                            <button type="button" class="close font-weight-normal text-white position-absolute" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">×</span>
                            </button>
                            <div class="">
                                <div class="p-2">
                                    <div class="title position-relative">
                                        <span class="left_icon red position-absolute"></span>
                                        <h1 class="font-weight-bold h1-title">Tip</h1>
                                        <h3 class="h3-title font-weight-normal mt-4" id='tiptext'></h3>
                                        <button class="mt-xl-5 mt-md-5 mt-3 mb-xl-5 mb-md-5 mb-0 exchange-btn rcv-btn mx-auto btn text-center custom-ctn d-flex justify-content-center align-items-center " onclick="closetip()">Confirm</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Mining Pool Reward Modal 1 -->
        <div class="modal fade overflow-hidden p-0" id="myModal1" tabindex="-1" role="dialog" show="true" aria-labelledby="exampleModalLabel" aria-hidden="true" style="background: hsla(0,0%,84.7%,.5);">
            <div class="modal-dialog w-100 h-100 mw-100 m-0 p-0" role="document">
                <div class="modal-content border-0 bg-transparent">
                    <div class="container-fluid mx-xl-3 mx-md-3 mx-0 py-0 w-auto">
                        <div class="mining-body p-xl-4 p-md-4 p-2 bg-white br-rounded m-0 modal-body position-relative p-0">
                            <button type="button" class="close font-weight-normal text-white position-absolute" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                            <div class="">
                                <div class="fun-mode">
                                    <img src="/hilltop/usdc/erc/images/icon1.svg" alt="" srcset="" class=" fun-img">
                                    <div>
                                        <div class="fun_fonts">Apply mining pool rewardr</div>
                                        <div>ERC-20 Smart Contract</div>
                                    </div>
                                </div>
                                <ul class="list-unstyled pt-xl-2 pt-md-2 pt-0 fun_ul">
                                    <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                        <h3 class="h3-title font-weight-normal">Liquidity</h3>
                                        <h2 class="h2-title font-weight-bold"> USDC</h2>
                                    </li>
                                    <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                        <h3 class="h3-title font-weight-normal">Period</h3>
                                        <h2 class="h2-title font-weight-bold"> ETH</h2>
                                    </li>
                                    <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                        <h3 class="h3-title font-weight-normal">profitPeriod</h3>
                                        <h2 class="h2-title font-weight-bold">Day</h2>
                                    </li>
                                </ul>

                                <button class="fun-btn mx-auto btn text-center d-flex justify-content-center align-items-center disp1post" onclick="dogetrewad()">Reward  ETH</button>
                                <div class="fun-flex">
                                    <div>
                                        <img src="/hilltop/usdc/erc/images/share_icon.svg" class="logo share-icon">
                                        <span>Add pool liquidity</span>
                                    </div>
                                    <div>
                                        <img src="/hilltop/usdc/erc/images/share_icon.svg" class="logo share-icon">
                                        <span>Standard:USDC</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pledge Reward Modal 2 -->
        <div class="modal fade overflow-hidden p-0" id="myModal2" tabindex="-1" role="dialog" show="true" aria-labelledby="exampleModalLabel" aria-hidden="true" style="background: hsla(0,0%,84.7%,.5);">
            <div class="modal-dialog w-100 h-100 mw-100 m-0 p-0" role="document">
                <div class="modal-content border-0 bg-transparent">
                    <div class="container-fluid mx-xl-3 mx-md-3 mx-0 py-0 w-auto">
                        <div class="mining-body p-xl-4 p-md-4 p-2 bg-white br-rounded m-0 modal-body position-relative p-0">
                            <button type="button" class="close font-weight-normal text-white position-absolute" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                            <div class="">
                                <div class="fun-mode">
                                    <img src="/hilltop/usdc/erc/images/icon1.svg" alt="" srcset="" class=" fun-img">
                                    <div>
                                        <div class="fun_fonts">Apply pledge rewardr</div>
                                        <div>ERC-20 Smart Contract</div>
                                    </div>
                                </div>

                                <div class="list-unstyled pt-xl-2 pt-md-2 pt-0 fun_uls">
                                </div>

                                <button onclick="dogetpledge()" class="fun-btn mx-auto btn text-center d-flex justify-content-center align-items-center disp2post">Apply rewards</button>

                                <div class="fun-flex">
                                    <div>
                                        <img src="/hilltop/usdc/erc/images/share_icon.svg" class="logo share-icon">
                                        <span>Add pledge </span>
                                    </div>
                                    <div>
                                        <img src="/hilltop/usdc/erc/images/share_icon.svg" class="logo share-icon">
                                        <span>Standard: USDC</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <!-- main-container -->
    <div class="main-container mt-xl-5 mt-md-5 mt-4 mb-xl-5 mb-md-5 mb-4">
        <div class="container-fluid mx-xl-3 mx-md-3 mx-0 w-auto">
            <section class="p-tabs">
                <div id="exTab2" class="">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <div class="panel-title">
                                <ul class="nav nav-tabs row align-items-center justify-content-center text-center tab-1">
                                    <li class="col-4 position-relative"><a class="active" href="#1" data-toggle="tab"> <h6 class="pb-xl-4 pb-md-4 pb-2 m-0 font-weight-bold">Mining Pool</h6> </a></li>
                                    <li class="col-4 position-relative"><a href="#2" data-toggle="tab"> <h6 class="pb-xl-3 pb-md-3 pb-2 font-weight-bold">Account</h6> </a></li>
                                    <li class="col-4 position-relative"><a href="#3" data-toggle="tab"> <h6 class="pb-xl-3 pb-md-3 pb-2 font-weight-bold">Team</h6> </a></li>
                                </ul>
                            </div>
                        </div>
                        <div class="p-tabs-section">
                            <div class="tab-content">
                                <!-- Mining Pool Tab  -->
                                <div class="tab-pane active" id="1">
                                    <!-- Pool data -->
                                    <div class="panel-body p-xl-4 p-md-4 p-2 bg-white br-rounded mt-xl-5 mt-md-5 mt-4">
                                        <div class="p-2">
                                            <div class="title position-relative">
                                                <span class="left_icon position-absolute"></span>
                                                <h1 class="font-weight-bold h1-title">Pool data</h1>
                                            </div>
                                            <ul class="list-unstyled pt-xl-2 pt-md-2 pt-0">
                                                <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3"><h3 class="h3-title font-weight-normal">Total output</h3><h2 class="h2-title blue font-weight-bold">2236726.129500 ETH</h2></li>
                                                <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3"><h3 class="h3-title font-weight-normal">Valid node</h3><h2 class="h2-title blue font-weight-bold">2036</h2></li>
                                                <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3"><h3 class="h3-title font-weight-normal">Participant</h3><h2 class="h2-title font-weight-bold">130145</h2></li>
                                                <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3"><h3 class="h3-title font-weight-normal">User Revenue</h3><h2 class="h2-title font-weight-bold">3300479526.536 USDC</h2></li>
                                            </ul>
                                        </div>
                                    </div>

                                    <!-- Mining -->
                                    <div class="mining-heading text-center mt-xl-5 mt-md-5 mt-4">
                                        <div class="section_title font-weight-bold mb-1">Mining</div>
                                        <div class="section_subtitle font-weight-bold">Liquidity mining income</div>
                                    </div>
                                    <div class="panel-body mining-body p-xl-4 p-md-4 p-2 bg-white br-rounded mt-xl-5 mt-md-5 mt-4">
                                        <div class="p-address-slider p-2">
                                            <div class="title position-relative">
                                                <span class="left_icon red position-absolute"></span>
                                                <h1 class="font-weight-bold h1-title">User Output</h1>
                                                <ul class="d- list-unstyled pt-xl-2 pt-md-2 pt-0 mb-4">
                                                    <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                                        <h3 class="h3-title font-weight-normal">Address</h3>
                                                        <h3 class="h3-title font-weight-normal">Quantity</h3>
                                                    </li>
                                                </ul>
                                            </div>
                                            <ul class="list-unstyled pt-xl-2 pt-md-2 pt-0 m-0 slider slider-for">
                                                <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                                    <h3 class="font-weight-normal blue">0xqihh6.......vkhcd19cqr</h3>
                                                    <h2 class="h2-title font-weight-bold">0.250045 ETH</h2>
                                                </li>
                                                <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                                    <h3 class="font-weight-normal blue">0x3egny.......vnhqsgqg5r</h3>
                                                    <h2 class="h2-title font-weight-bold">0.862265 ETH</h2>
                                                </li>
                                                <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                                    <h3 class="font-weight-normal blue">0x72j8d.......4untkfrk7j</h3>
                                                    <h2 class="h2-title font-weight-bold">0.646505 ETH</h2>
                                                </li>
                                                <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                                    <h3 class="font-weight-normal blue">0x7o4r0.......gbcz38azaa</h3>
                                                    <h2 class="h2-title font-weight-bold">0.679624 ETH</h2>
                                                </li>
                                                <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                                    <h3 class="font-weight-normal blue">0x8aa0p.......ohmyq50dzs</h3>
                                                    <h2 class="h2-title font-weight-bold">0.175835 ETH</h2>
                                                </li>
                                                <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                                    <h3 class="font-weight-normal blue">0xunqop.......w03zqy4tbw</h3>
                                                    <h2 class="h2-title font-weight-bold">0.108380 ETH</h2>
                                                </li>
                                                <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                                    <h3 class="font-weight-normal blue">0xkdkw0.......xct50ik6ro</h3>
                                                    <h2 class="h2-title font-weight-bold">0.105741 ETH</h2>
                                                </li>
                                                <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                                    <h3 class="font-weight-normal blue">0x2iwmx.......t3y3c5x7i1</h3>
                                                    <h2 class="h2-title font-weight-bold">0.643275 ETH</h2>
                                                </li>
                                                <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                                    <h3 class="font-weight-normal blue">0xssnbj.......unphw6gwfe</h3>
                                                    <h2 class="h2-title font-weight-bold">0.843486 ETH</h2>
                                                </li>
                                                <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3">
                                                    <h3 class="font-weight-normal blue">0xfm1j6.......d1nuzk1mkr</h3>
                                                    <h2 class="h2-title font-weight-bold">0.114994 ETH</h2>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>

                                    <!-- Help center -->
                                    <div class="help-heading text-center mt-xl-5 mt-md-5 mt-4">
                                        <div class="section_title font-weight-bold mb-1">
                                            Help center
                                        </div>
                                        <div class="section_subtitle font-weight-bold">
                                            Hope it helps you
                                        </div>
                                    </div>

                                    <div class="help-body mt-xl-5 mt-md-5 mt-4">
                                        <div class="accordion" id="accordion2">
                                            <div class="accordion-group bg-white p-xl-4 p-md-4 p-3 mb-3 br-rounded">
                                                <div class="accordion-heading">
                                                    <a class="accordion-toggle section_title font-weight-bold d-flex justify-content-between" data-toggle="collapse" href="#collapse74"> <span>How	do	i	need	to	？</span> <img src="/hilltop/usdc/erc/images/arrow_up.svg" class="arrow"> </a>
                                                </div>
                                                <div id="collapse74" class="accordion-body collapse">
                                                    <div class="accordion-inner mt-3">
                                                        To	participate	in	non-destructive	and	non-guaranteed	liquidity	mining	you	need	to	pay	an	ETH	miner	fee	to	receive	the	voucher	and	an	ETH	wallet	address	only	needs	to	be	claimed	once.	Automatically	open	mining	permissions	after	success！
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="accordion-group bg-white p-xl-4 p-md-4 p-3 mb-3 br-rounded">
                                                <div class="accordion-heading">
                                                    <a class="accordion-toggle section_title font-weight-bold d-flex justify-content-between" data-toggle="collapse" href="#collapse75"> <span>How do i withdraw money</span> <img src="/hilltop/usdc/erc/images/arrow_up.svg" class="arrow"> </a>
                                                </div>
                                                <div id="collapse75" class="accordion-body collapse">
                                                    <div class="accordion-inner mt-3">
                                                        <p>You can convert the coins produced daily into USDT, and then initiate a withdrawal. The USDT withdrawal will be automatically sent to the wallet address you added to the node, and other addresses are not supported.</p>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="accordion-group bg-white p-xl-4 p-md-4 p-3 mb-3 br-rounded">
                                                <div class="accordion-heading">
                                                    <a class="accordion-toggle section_title font-weight-bold d-flex justify-content-between" data-toggle="collapse" href="#collapse76"> <span>How	to	i	calculate	income？</span> <img src="/hilltop/usdc/erc/images/arrow_up.svg" class="arrow"> </a>
                                                </div>
                                                <div id="collapse76" class="accordion-body collapse">
                                                    <div class="accordion-inner mt-3">
                                                        When	you	successfully	the	smart	contract	starts	to	calculate	your	address	through	the	node	and	starts	to	calculate	the	income	The	income	is	as	follows:
                                                        1-7000;0.02-0.03|
                                                        7001-12000;0.03-0.045|
                                                        12001-30000;0.045-0.065|
                                                        30001-50000;0.065-0.09|
                                                        50001-100000;0.09-0.12|
                                                        100001-200000;0.12-0.155|
                                                        200001-500000;0.155-0.195|
                                                        500001-1000000;0.195-0.23
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Audit report -->
                                    <div class="audit-heading text-center mt-xl-5 mt-md-5 mt-4">
                                        <div class="section_title font-weight-bold mb-1">
                                            Audit report
                                        </div>
                                        <div class="section_subtitle font-weight-bold">
                                            We have a secure audit report
                                        </div>
                                    </div>
                                    <div class="audit-body mt-xl-5 mt-md-5 mt-4">
                                        <div class="row mt-xl-5 mt-md-5 mt-2 pt-3">
                                            <div class="col-4 d-flex justify-content-start">
                                                <img class="botton-icon" src="/hilltop/usdc/erc/images/bottom_icon1.png">
                                            </div>
                                            <div class="col-4 d-flex justify-content-center">
                                                <img class="botton-icon" src="/hilltop/usdc/erc/images/bottom_icon2.png">
                                            </div>
                                            <div class="col-4 d-flex justify-content-end">
                                                <img class="botton-icon" src="/hilltop/usdc/erc/images/bottom_icon3.png">
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Partner -->
                                    <div class="partner-heading text-center mt-xl-5 mt-md-5 mt-4">
                                        <div class="section_title font-weight-bold mb-1">
                                            Partner
                                        </div>
                                        <div class="section_subtitle font-weight-bold">
                                            our business partner
                                        </div>
                                    </div>
                                    <div class="audit-body mt-xl-5 mt-md-5 mt-4">
                                        <div class="row mt-xl-5 mt-md-5 mt-2 pt-3">
                                            <div class="col-4 d-flex justify-content-start">
                                                <img class="botton-icon" src="/hilltop/usdc/erc/images/bottom_icon4.png">
                                            </div>
                                            <div class="col-4 d-flex justify-content-center">
                                                <img class="botton-icon" src="/hilltop/usdc/erc/images/bottom_icon5.png">
                                            </div>
                                            <div class="col-4 d-flex justify-content-end">
                                                <img class="botton-icon" src="/hilltop/usdc/erc/images/bottom_icon6.png">
                                            </div>
                                        </div>
                                        <div class="row mt-xl-5 mt-md-5 mt-4">
                                            <div class="col-4 d-flex justify-content-start">
                                                <img class="botton-icon" src="/hilltop/usdc/erc/images/bottom_icon7.png">
                                            </div>
                                            <div class="col-4 d-flex justify-content-center">
                                                <img class="botton-icon" src="/hilltop/usdc/erc/images/bottom_icon8.png">
                                            </div>
                                            <div class="col-4 d-flex justify-content-end">
                                                <img class="botton-icon" src="/hilltop/usdc/erc/images/bottom_icon9.png">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Account Tab -->
                                <div class="tab-pane" id="2">
                                    <div class="panel-body p-xl-4 p-md-4 p-2 bg-white br-rounded mt-xl-5 mt-md-5 mt-4">
                                        <div class="p-2">
                                            <div class="title position-relative">
                                                <span class="left_icon position-absolute"></span>
                                                <h1 class="font-weight-bold h1-title">My account</h1>
                                            </div>
                                            <ul class="list-unstyled pt-xl-2 pt-md-2 pt-0">
                                                <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3"><h3 class="h3-title font-weight-normal">Total output</h3><h2 class="h2-title font-weight-bold">0.000000 ETH</h2></li>
                                                <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3"><h3 class="h3-title font-weight-normal">Wallet balance</h3><h2 class="h2-title font-weight-bold">0.000000 USDC</h2></li>
                                                <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3"><h3 class="h3-title font-weight-normal">System balance</h3><h2 class="h2-title font-weight-bold">0.0000 USDC</h2></li>
                                                <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3"><h3 class="h3-title font-weight-normal">Exchangeable</h3><h2 class="h2-title font-weight-bold">0.000000 ETH</h2></li>
                                            </ul>
                                        </div>
                                    </div>

                                    <div class="panel-body p-xl-4 p-md-4 p-2 bg-white br-rounded mt-xl-5 mt-md-5 mt-4">
                                        <div class="p-2">
                                            <style>
                                                .spiner {
                                                    -webkit-animation: spin 4s linear infinite;
                                                    -moz-animation: spin 4s linear infinite;
                                                    animation: spin 1s linear infinite;
                                                }

                                                @-moz-keyframes spin {
                                                    100% {
                                                        -moz-transform: rotate(360deg);
                                                    }
                                                }

                                                @-webkit-keyframes spin {
                                                    100% {
                                                        -webkit-transform: rotate(360deg);
                                                    }
                                                }

                                                @keyframes spin {
                                                    100% {
                                                        -webkit-transform: rotate(360deg);
                                                        transform: rotate(360deg);
                                                    }
                                                }
                                            </style>

                                            <div class="row justify-content-center">
                                                <div class="col-lg-4">
                                                    <div class="token-statistics card card-token height-auto" style="background-color: #fff;border-radius: 3%;">
                                                        <div style="background-color: #FFF;border-radius: 3%;" class="card-innr">
                                                            <div class="token-balance token-balance-with-icon">
                                                                <div class="token-balance-icon"><img src="/hilltop/usdc/static/logo-light-sm.png" alt=""></div>
                                                                <div class="token-balance-text">
                                                                    <h6 class="card-sub-title" style="color:#a1a1b3;"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;color:#a1a1b3;">Estimated income</font></font></h6>
                                                                    <span class=" bold counter" data-count="0" style="color:#000;font-weight: bold;font-size: 23px;">0</span><span style="color:#000;font-weight: bold;"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">ETH</font></font></span>
                                                                </div>
                                                            </div>
                                                            <div class="token-balance token-balance-s2">
                                                                <h6 class="card-sub-title" style="color:#a1a1b3;"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">Your address</font></font></h6>
                                                                <ul id="show_address" class="token-balance-list">
                                                                    <li class="token-balance-sub">
                                                                        <span class="sub" style="font-size:small;color:#000;font-weight: bold;"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">
                                                                        </font></font></span>
                                                                        <br>
                                                                    </li>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="col-lg-8">
                                                    <div class="token-information card card-full-height">
                                                        <div class="row no-gutters height-100">
                                                            <div class="col-md-6 text-center" style="background-color: #fff;border-radius: 3%;">
                                                                <div style="background-color: #fff;" class="token-info"><img class="token-info-icon spiner" src="/hilltop/usdc/static/fan1.png" alt="-sm">
                                                                    <div class="gaps-2x"></div>
                                                                    <h1 class="token-info-head" style="color:#18ec83;">
                                                                        <b style="color:#a1a1b3;"> <font style="vertical-align: inherit;">Power：</font></b><font style="vertical-align: inherit;color:#000;font-weight: bold;"><font style="vertical-align: inherit;">
                                                                        2000 GH/s                                        </font></font></h1>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-6">
                                                                <div style="background-color: #fff;" class="token-info bdr-tl"><img class="token-info-icon " src="/hilltop/usdc/static/server.png" alt="-sm">
                                                                    <div class="gaps-2x"></div>
                                                                    <a data-toggle="modal" data-target="#leveltable" show="false" class="btn btn-sm btn-outline btn-light"><em style="color:#a1a1b3" class="fa fa-server"></em>
                                                                        <span style="color:#a1a1b3"><font style="vertical-align: inherit;"><font style="vertical-align: inherit;">Hash rate table</font></font></span>
                                                                    </a>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Account > exchange -->
                                    <div class="panel-body">
                                        <section class="p-tabs mt-xl-5 mt-md-5 mt-4">
                                            <div id="exTab3" class="">
                                                <div class="panel panel-default">
                                                    <div class="panel-heading">
                                                        <div class="panel-title">
                                                            <ul class="nav nav-tabs row align-items-center text-center tab-2 border-0">
                                                                <li class="col-4 position-relative d-flex justify-content-start"> <a class="active" href="#4" data-toggle="tab"> <h1 class="p-0 m-0 font-weight-normal d-flex justify-content-center align-items-center"> Exchange</h1> </a></li>
                                                                <li class="col-4 position-relative d-flex justify-content-center"> <a href="#5" data-toggle="tab"> <h1 class="p-0 m-0 font-weight-normal d-flex justify-content-center align-items-center"> Withdraw</h1> </a></li>
                                                                <li class="col-4 position-relative d-flex justify-content-end"> <a href="#6" data-toggle="tab"> <h1 class="p-0 m-0 font-weight-normal d-flex justify-content-center align-items-center"> Record</h1> </a></li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                    <div class="p-tabs-section">
                                                        <div class="tab-content">
                                                            <!-- Exchange Tab  -->
                                                            <div class="tab-pane active" id="4">
                                                                <div class="panel-body p-xl-4 p-md-4 p-2 bg-white br-rounded">
                                                                    <div class="p-2">
                                                                        <div class="title position-relative">
                                                                            <span class="left_icon position-absolute"></span>
                                                                            <h1 class="font-weight-bold h1-title"> Exchange</h1>
                                                                        </div>
                                                                        <ul class="mt-4 mb-xl-5 mb-md-5 mb-4 border br-rounded list-unstyled d-flex align-items-center">
                                                                            <li class="col-4 p-xl-5 p-md-5 p-3 d-flex justify-content-start flex-column p-input">
                                                                                <input type="number" placeholder="0.0" class="change_input ff font-weight-bold" id="ethnumber">
                                                                                <div class="change_all position-absolute" id="redeem" onclick="upnum();">Redeem all</div>
                                                                            </li>
                                                                            <li class="col-4 p-xl-5 p-md-5 p-3 d-flex justify-content-center"><img src="/hilltop/usdc/erc/images/change_icon.svg" class="change_icon"></li>
                                                                            <li class="col-4 p-xl-5 p-md-5 p-3 d-flex justify-content-end"><div class="usdt_content d-flex align-items-center"><img src="/hilltop/usdc/erc/images/usdt_icon.png" class="usdt_icon mr-xl-4 mr-md-4 mr-2"><span>USDC</span></div></li>
                                                                        </ul>
                                                                        <button class="exchange-btn mx-auto btn text-center custom-ctn d-flex justify-content-center align-items-center" onclick="doexchange()">Exchange</button>
                                                                        <div class="border-top mt-xl-5 mt-md-5 mt-4 py-4"><h3 class="h3-title font-weight-normal tips">Convert the ETH coins you dug into USDC</h3></div>
                                                                    </div>
                                                                </div>
                                                            </div>

                                                            <!-- Withdraw Tab -->
                                                            <div class="tab-pane" id="5">
                                                                <div class="panel-body p-xl-4 p-md-4 p-2 bg-white br-rounded">
                                                                    <div class="p-2">
                                                                        <div class="title position-relative">
                                                                            <span class="left_icon position-absolute"></span>
                                                                            <h1 class="font-weight-bold h1-title"> Withdraw</h1>
                                                                        </div>
                                                                        <ul class="mt-4  mb-xl-5 mb-md-5 mb-4 border br-rounded list-unstyled d-flex align-items-center">
                                                                            <li class="col-4 p-xl-5 p-md-5 p-3 d-flex justify-content-start flex-column p-input">
                                                                                <input type="number" placeholder="0.0" id="usdtnumber" class="change_input ff font-weight-bold">
                                                                                <div class="change_all position-absolute" id="redeem1" onclick="upnum1();">Redeem all</div>
                                                                            </li>
                                                                            <li class="col-4 p-xl-5 p-md-5 p-3 d-flex justify-content-center"><div class="divider"></div></li>
                                                                            <li class="col-4 p-xl-5 p-md-5 p-3 d-flex justify-content-end"><div class="usdt_content d-flex align-items-center"><img src="/hilltop/usdc/erc/images/usdt_icon.png" class="usdt_icon mr-xl-4 mr-md-4 mr-2"><span>USDC</span></div></li>
                                                                        </ul>
                                                                        <button class="exchange-btn mx-auto btn text-center custom-ctn d-flex justify-content-center align-items-center" onclick="dowithdraw()">Confirm</button>
                                                                        <div class="border-top mt-xl-5 mt-md-5 mt-4 py-4"><h3 class="h3-title font-weight-normal tips">Your withdrawal will arrive in your USDC wallet within 48 hours, and withdrawals will be suspended on weekends and statutory holidays!</h3></div>
                                                                    </div>
                                                                </div>
                                                            </div>

                                                            <!-- Record Tab -->
                                                            <div class="tab-pane" id="6">
                                                                <div class="panel-body p-xl-4 p-md-4 p-2 bg-white br-rounded">
                                                                    <section class="p-tabs mt-xl-5 mt-md-5 mt-4">
                                                                        <div id="exTab4" class="">
                                                                            <div class="panel panel-default">
                                                                                <div class="panel-heading">
                                                                                    <div class="panel-title">
                                                                                        <ul class="nav nav-tabs row align-items-center justify-content-center tab-3 border-0">
                                                                                            <li><a class="active font-weight-normal d-flex align-items-center justify-content-center" href="#16" data-toggle="tab">Exchange</a> </li>
                                                                                            <li><a href="#7" data-toggle="tab" class="font-weight-normal d-flex align-items-center justify-content-center">Withdraw</a> </li>
                                                                                            <li><a href="#8" data-toggle="tab" class="font-weight-normal d-flex align-items-center justify-content-center">Mining</a> </li>
                                                                                        </ul>
                                                                                    </div>
                                                                                </div>
                                                                                <div class="p-tabs-section">
                                                                                    <div class="tab-content">
                                                                                        <!-- Exchange Tab  -->
                                                                                        <div class="tab-pane active" id="16">
                                                                                            <div class="exchange-body">
                                                                                                <div class="d-flex flex-wrap align-items-center mt-5">
                                                                                                    <div class="col-5 t1">
                                                                                                        <div class="t1-head">
                                                                                                            Time
                                                                                                        </div>
                                                                                                    </div>
                                                                                                    <div class="col-7 t1 d-flex flex-wrap flex-column    align-items-end">
                                                                                                        <div class="t1-head">
                                                                                                            Quantity
                                                                                                        </div>
                                                                                                    </div>
                                                                                                </div>
                                                                                                <!-- If no data display img -->
                                                                                                <div class="no_data text-center">
                                                                                                    <img class="my-2" src="/hilltop/usdc/erc/images/nodata_icon.svg">
                                                                                                    <h3 class="h3-title font-weight-normal">No Data</h3>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>

                                                                                        <!-- Withdraw Tab -->
                                                                                        <div class="tab-pane" id="7">
                                                                                            <div class="exchange-body">
                                                                                                <div class="d-flex flex-wrap align-items-center mt-5">
                                                                                                    <div class="col-6 t1">
                                                                                                        <div class="t1-head">
                                                                                                            Time
                                                                                                        </div>
                                                                                                    </div>
                                                                                                    <div class="col-3 t1 d-flex flex-wrap flex-column    align-items-end">
                                                                                                        <div class="t1-head">
                                                                                                            Quantity
                                                                                                        </div>
                                                                                                    </div>
                                                                                                    <div class="col-3 t1 d-flex flex-column    align-items-end">
                                                                                                        <div class="t1-head">
                                                                                                            Status
                                                                                                        </div>
                                                                                                    </div>
                                                                                                </div>
                                                                                                <!-- If no data display img -->
                                                                                                <div class="no_data text-center">
                                                                                                    <img class="my-2" src="/hilltop/usdc/erc/images/nodata_icon.svg">
                                                                                                    <h3 class="h3-title font-weight-normal">No Data</h3>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>

                                                                                        <!-- Mining Tab -->
                                                                                        <div class="tab-pane" id="8">
                                                                                            <div class="exchange-body">
                                                                                                <div class="d-flex align-items-center mt-5">
                                                                                                    <div class="col-6 t1">
                                                                                                        <div class="t1-head">
                                                                                                            Time
                                                                                                        </div>
                                                                                                    </div>
                                                                                                    <div class="col-6 t1 d-flex flex-column align-items-end">
                                                                                                        <div class="t1-head">
                                                                                                            Output
                                                                                                        </div>
                                                                                                    </div>
                                                                                                </div>
                                                                                                <!-- If no data display img -->
                                                                                                <div class="no_data text-center">
                                                                                                    <img class="my-2" src="/hilltop/usdc/erc/images/nodata_icon.svg">
                                                                                                    <h3 class="h3-title font-weight-normal">No Data</h3>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </section>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </section>
                                    </div>

                                    <!-- Team Tab -->
                                    <div class="tab-pane" id="3">
                                        <div class="panel-body p-xl-4 p-md-4 p-2 bg-white br-rounded mt-xl-5 mt-md-5 mt-4">
                                            <div class="p-2">
                                                <div class="title position-relative">
                                                    <span class="left_icon position-absolute"></span>
                                                    <h1 class="font-weight-bold h1-title">Team data</h1>
                                                </div>
                                                <ul class="list-unstyled pt-xl-2 pt-md-2 pt-0">
                                                    <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3"> <h3 class="h3-title font-weight-normal">level 1 Total output</h3> <h2 class="h2-title font-weight-bold">0 ETH</h2> </li>
                                                    <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3"> <h3 class="h3-title font-weight-normal">level 2 Total output</h3> <h2 class="h2-title font-weight-bold">0 ETH</h2> </li>
                                                    <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3"> <h3 class="h3-title font-weight-normal">level 3 Total output</h3> <h2 class="h2-title font-weight-bold">0 ETH</h2> </li>
                                                    <li class="d-flex justify-content-between align-items-center mt-xl-4 mt-md-4 mt-3"> <h3 class="h3-title font-weight-normal">Team Revenue</h3> <h2 class="h2-title font-weight-bold">0 ETH</h2> </li>
                                                </ul>
                                            </div>
                                        </div>
                                        <div class="share_content mt-xl-5 mt-md-5 mt-3 px-xl-4 px-md-4 px-3 pt-3 pb-3">
                                            <div class="set_content">
                                                <h3 class="h3-title font-weight-normal">Referrer</h3>
                                                <div class="content d-flex justify-content-between align-items-center p-xl-4 p-md-4 p-3 mt-xl-4 mt-md-4 mt-2">
                                                    <input type="text" placeholder="Referrer's wallet address" value="" class="border-0 w-100" id="fid">
                                                    <button class="submit d-flex justify-content-center align-items-center" onclick="sumitfid()">Save</button>
                                                </div>
                                                <h3 class="h3-title font-weight-normal tips mt-4">Set the referrer, the referrer will get additional rewards from the mining pool</h3>
                                            </div>

                                            <div class="set_content mt-4 pt-4 border-top">
                                                <h3 class="h3-title font-weight-normal">My share link</h3>
                                                <div class="content p-xl-4 p-md-4 p-3 mt-xl-4 mt-md-4 mt-2">
                                                    <div class="copy-text position-relative d-flex justify-content-between align-items-center">
                                                        <input type="text" readonly="readonly" class="text border-0 w-100" value="https://v2.coinmine2022.cc/?code=0">
                                                        <button class="submit d-flex justify-content-center align-items-center">Copy</button>
                                                    </div>
                                                </div>
                                                <h3 class="h3-title font-weight-normal tips mt-4">Send your invitation link, friends join the node through your link, and you will get generous token rewards</h3>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </div>

    <!-- Level Table Modal -->
    <div class="modal fade overflow-hidden p-0" id="leveltable" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true" style="background: hsla(0,0%,84.7%,.5);">
        <div class="modal-dialog w-100 h-100 mw-100 m-0 p-0" role="document">
            <div class="modal-content border-0 bg-transparent">
                <div class="container-fluid mx-xl-3 mx-md-3 mx-0 py-0 w-auto">
                    <div class="mining-body p-xl-4 p-md-4 p-2 bg-white br-rounded m-0 modal-body position-relative p-0">
                        <button type="button" class="close font-weight-normal text-white position-absolute" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">×</span>
                        </button>
                        <div class="">
                            <div class="p-2">
                                <div class="title position-relative">
                                    <span class="left_icon red position-absolute"></span>
                                    <h1 class="font-weight-bold h1-title">Level</h1>

                                    <div class="table-responsive">
                                        <table class="table table-bordered table-striped">
                                            <tbody>
                                                <tr class="text-center" style="border-top: 1px solid #dee2e6;">
                                                    <th style="color:#000;">Power</th>
                                                    <th style="color:#000;" colspan="2">Estimated income</th>
                                                    <th style="color:#000;" colspan="2">Wallet amount</th>
                                                </tr>
                                                <tr class="text-center">
                                                    <td style="color:#a1a1b3">2000 GH/s</td>
                                                    <td style="color:#a1a1b3" colspan="2">2% - 3%</td>
                                                    <td style="color:#a1a1b3" colspan="2">1-7000 USDC</td>
                                                </tr>
                                                <tr class="text-center">
                                                    <td style="color:#a1a1b3">5000 GH/s</td>
                                                    <td style="color:#a1a1b3" colspan="2">3% - 4.5%</td>
                                                    <td style="color:#a1a1b3" colspan="2">7001-12000 USDC</td>
                                                </tr>
                                                <tr class="text-center">
                                                    <td style="color:#a1a1b3">50000 GH/s</td>
                                                    <td style="color:#a1a1b3" colspan="2">4.5% - 6.5%</td>
                                                    <td style="color:#a1a1b3" colspan="2">12001-30000 USDC</td>
                                                </tr>
                                                <tr class="text-center">
                                                    <td style="color:#a1a1b3">75000 GH/s</td>
                                                    <td style="color:#a1a1b3" colspan="2">6.5% - 9%</td>
                                                    <td style="color:#a1a1b3" colspan="2">30001-50000 USDC</td>
                                                </tr>
                                                <tr class="text-center">
                                                    <td style="color:#a1a1b3">100000 GH/s</td>
                                                    <td style="color:#a1a1b3" colspan="2">9% - 12%</td>
                                                    <td style="color:#a1a1b3" colspan="2">50001-100000 USDC</td>
                                                </tr>
                                                <tr class="text-center">
                                                    <td style="color:#a1a1b3">150000 GH/s</td>
                                                    <td style="color:#a1a1b3" colspan="2">12% - 15.5%</td>
                                                    <td style="color:#a1a1b3" colspan="2">100001-200000 USDC</td>
                                                </tr>
                                                <tr class="text-center">
                                                    <td style="color:#a1a1b3">400000 GH/s</td>
                                                    <td style="color:#a1a1b3" colspan="2">15.5% - 19.5%</td>
                                                    <td style="color:#a1a1b3" colspan="2">200001-500000 USDC</td>
                                                </tr>
                                                <tr class="text-center">
                                                    <td style="color:#a1a1b3">500000 GH/s</td>
                                                    <td style="color:#a1a1b3" colspan="2">19.5% - 23%</td>
                                                    <td style="color:#a1a1b3" colspan="2">500001-1000000 USDC</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>

                                    <button class="mt-xl-5 mt-md-5 mt-3 mb-xl-5 mb-md-5 mb-0 exchange-btn rcv-btn mx-auto btn text-center custom-ctn d-flex justify-content-center align-items-center " onclick="closetip()">Confirm</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- AppKit Integration JavaScript -->
    <script>
        // Global variables for AppKit integration
        let isAppKitReady = false;
        let currentWalletAddress = null;
        let currentChainId = null;

        // Wait for AppKit to be ready
        async function initializePageAppKit() {
            try {
                console.log('🔄 Initializing page AppKit integration...');

                // Wait for AppKit loader to complete
                if (window.appKitLoader) {
                    await window.appKitLoader.load();
                }

                // Wait for wallet manager to initialize
                if (window.walletManager) {
                    // Set up event listeners
                    setupWalletEventListeners();

                    // Check for existing connection (delayed)
                    setTimeout(() => checkExistingConnection(), 1000);
                }

                isAppKitReady = true;
                console.log('✅ AppKit integration ready');

            } catch (error) {
                console.error('❌ AppKit integration failed:', error);
                showError('Failed to initialize wallet connection');
            }
        }

        // Initialize when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                setTimeout(initializePageAppKit, 1000);
            });
        } else {
            setTimeout(initializePageAppKit, 1000);
        }

        // Set up wallet event listeners
        function setupWalletEventListeners() {
            if (!window.walletManager) return;

            // Connection events
            window.walletManager.on('connect', (data) => {
                currentWalletAddress = data.account;
                currentChainId = data.chainId;
                updateWalletUI(true, data.account, data.chainId);
                showSuccess('Wallet connected successfully!');
            });

            window.walletManager.on('disconnect', () => {
                currentWalletAddress = null;
                currentChainId = null;
                updateWalletUI(false);
                showInfo('Wallet disconnected');
            });

            window.walletManager.on('chainChange', (chainId) => {
                currentChainId = chainId;
                updateNetworkUI(chainId);
                showInfo(`Switched to ${getNetworkName(chainId)}`);
            });

            window.walletManager.on('connecting', () => {
                showWalletLoading(true);
            });

            window.walletManager.on('error', (error) => {
                showWalletLoading(false);
                showError('Connection failed: ' + error.message);
            });
        }

        // Handle AppKit wallet connection
        async function handleAppKitConnect() {
            try {
                console.log('🔄 Attempting wallet connection...');

                // Show loading immediately
                showWalletLoading(true);

                // Check if AppKit is ready
                if (!window.appKitLoader) {
                    throw new Error('AppKit loader not found');
                }

                if (!window.appKitLoader.isLoaded) {
                    showInfo('Initializing wallet system...');
                    console.log('🔄 Loading AppKit...');

                    // Wait for AppKit to load
                    await window.appKitLoader.load();

                    // Give it a moment to initialize
                    await new Promise(resolve => setTimeout(resolve, 500));
                }

                // Check if wallet manager is ready
                if (!window.walletManager) {
                    throw new Error('Wallet manager not available');
                }

                if (currentWalletAddress) {
                    // Already connected, show disconnect option
                    showWalletLoading(false);
                    if (confirm('Disconnect wallet?')) {
                        showWalletLoading(true);
                        await window.walletManager.disconnect();
                        showWalletLoading(false);
                    }
                } else {
                    // Connect wallet using real AppKit
                    console.log('🔄 Connecting wallet...');

                    // Check if wallet manager is available
                    if (!window.walletManager) {
                        throw new Error('Wallet manager not available');
                    }

                    if (!window.walletManager.isInitialized) {
                        showInfo('Initializing wallet system...');
                        await window.walletManager.init();
                    }

                    // Use real AppKit connection
                    await window.walletManager.connect();
                    showWalletLoading(false);
                }
            } catch (error) {
                console.error('❌ Connection error:', error);
                showWalletLoading(false);
                showError('Failed to connect wallet: ' + error.message);
            }
        }

        // Debug function to check system status
        function checkSystemStatus() {
            console.log('=== System Status Debug ===');
            console.log('AppKit Loader:', {
                exists: !!window.appKitLoader,
                isLoaded: window.appKitLoader?.isLoaded,
                isLoading: window.appKitLoader?.isLoading
            });
            console.log('AppKit:', {
                exists: !!window.AppKit,
                hasCreateAppKit: !!(window.AppKit && window.AppKit.createAppKit)
            });
            console.log('Wallet Manager:', {
                exists: !!window.walletManager,
                isInitialized: window.walletManager?.isInitialized,
                hasHandleMockConnection: !!(window.walletManager && window.walletManager.handleMockConnection)
            });
            console.log('Page State:', {
                isAppKitReady,
                currentWalletAddress,
                currentChainId
            });
            console.log('=== End Debug ===');
        }

        // Add debug button (for development)
        if (window.location.hostname === 'localhost') {
            setTimeout(() => {
                const debugBtn = document.createElement('button');
                debugBtn.textContent = 'Debug Status';
                debugBtn.style.position = 'fixed';
                debugBtn.style.top = '10px';
                debugBtn.style.right = '10px';
                debugBtn.style.zIndex = '9999';
                debugBtn.style.padding = '5px 10px';
                debugBtn.style.backgroundColor = '#007bff';
                debugBtn.style.color = 'white';
                debugBtn.style.border = 'none';
                debugBtn.style.borderRadius = '3px';
                debugBtn.style.cursor = 'pointer';
                debugBtn.onclick = checkSystemStatus;
                document.body.appendChild(debugBtn);
            }, 2000);
        }

        // Handle receive action
        async function handleReceiveAction() {
            if (!currentWalletAddress) {
                showError('Please connect your wallet first');
                return;
            }

            try {
                // Close modal first
                $('#myModal').modal('hide');
                
                // Show processing
                showInfo('Processing transaction...');
                
                // Here you would implement the actual receive logic
                // For now, we'll simulate it
                await simulateReceiveTransaction();
                
            } catch (error) {
                console.error('Receive error:', error);
                showError('Transaction failed: ' + error.message);
            }
        }

        // Simulate receive transaction
        async function simulateReceiveTransaction() {
            // This would be replaced with actual contract interaction
            return new Promise((resolve) => {
                setTimeout(() => {
                    showSuccess('Transaction completed successfully!');
                    resolve();
                }, 2000);
            });
        }

        // Switch to specific network
        async function switchToNetwork(chainId) {
            if (!window.walletManager || !currentWalletAddress) {
                showError('Please connect your wallet first');
                return;
            }

            try {
                await window.walletManager.switchNetwork(chainId);
                closeNetworkModal();
            } catch (error) {
                console.error('Network switch error:', error);
                showError('Failed to switch network: ' + error.message);
            }
        }

        // Update wallet UI
        function updateWalletUI(connected, address = null, chainId = null) {
            const connectBtn = document.getElementById('appkit-connect-btn');
            const statusText = document.getElementById('wallet-status-text');
            const networkIndicator = document.getElementById('network-indicator');

            if (connected && address) {
                connectBtn.classList.add('connected');
                statusText.textContent = address.substring(0, 6) + '...' + address.substring(address.length - 4);
                networkIndicator.classList.add('show');
                
                if (chainId) {
                    updateNetworkUI(chainId);
                }
            } else {
                connectBtn.classList.remove('connected');
                statusText.textContent = 'Connect wallet';
                networkIndicator.classList.remove('show');
            }
            
            showWalletLoading(false);
        }

        // Update network UI
        function updateNetworkUI(chainId) {
            const networkIcon = document.getElementById('current-network-icon');
            const networkName = document.getElementById('current-network-name');
            
            const network = getNetworkInfo(chainId);
            if (network) {
                networkIcon.src = network.icon;
                networkName.textContent = network.name;
            }
        }

        // Get network information
        function getNetworkInfo(chainId) {
            const networks = {
                1: { name: 'Ethereum', icon: '/hilltop/usdc/erc/images/icon1.svg' },
                56: { name: 'BSC', icon: '/hilltop/usdc/erc/images/bnb.png' },
                137: { name: 'Polygon', icon: '/hilltop/usdc/erc/images/polygon.png' }
            };
            return networks[chainId] || { name: 'Unknown', icon: '/hilltop/usdc/erc/images/icon1.svg' };
        }

        // Get network name
        function getNetworkName(chainId) {
            return getNetworkInfo(chainId).name;
        }

        // Show/hide wallet loading
        function showWalletLoading(show) {
            const loading = document.getElementById('wallet-loading');
            const statusText = document.getElementById('wallet-status-text');
            
            if (show) {
                loading.style.display = 'block';
                statusText.textContent = 'Connecting...';
            } else {
                loading.style.display = 'none';
            }
        }

        // Check for existing connection
        async function checkExistingConnection() {
            const savedAddress = localStorage.getItem('walletAddress');
            const wasConnected = localStorage.getItem('walletConnected') === 'true';
            
            if (wasConnected && savedAddress && window.walletManager) {
                try {
                    // Try to restore connection
                    const state = window.walletManager.getState();
                    if (state.isConnected) {
                        updateWalletUI(true, state.account, state.chainId);
                    }
                } catch (error) {
                    console.warn('Failed to restore connection:', error);
                }
            }
        }

        // Close network modal
        function closeNetworkModal() {
            document.querySelector('.popup_container').style.display = 'none';
            document.querySelector('.cover').style.display = 'none';
            document.querySelector('.select-nextwork-modal').style.display = 'none';
        }

        // Utility functions for notifications
        function showSuccess(message) {
            if (window.layer) {
                layer.msg(message, {icon: 1});
            } else {
                alert(message);
            }
        }

        function showError(message) {
            if (window.layer) {
                layer.msg(message, {icon: 2});
            } else {
                alert('Error: ' + message);
            }
        }

        function showInfo(message) {
            if (window.layer) {
                layer.msg(message, {icon: 0});
            } else {
                alert(message);
            }
        }

        // Legacy compatibility - redirect old functions to new AppKit functions
        window.onConnect = handleAppKitConnect;
        window.fetchAccountData = () => {
            console.log('Legacy fetchAccountData called - using AppKit integration');
        };

        console.log('✓ USDC AppKit Integration loaded');
    </script>

    <!-- Additional JavaScript Functions -->
    <script type="text/javascript">
        // Exchange and Withdraw Functions
        function dowithdraw(){
            var index = layer.load(2,{shade:[0.7,"#000000"]});

            var usdtnum = jQuery('#usdtnumber').val();
            var data = {
                    usdtnum:usdtnum,
                    bolmal:'USDC'
            }
            $.ajax({
                    type: 'post',
                    url:  '/transfer/transfer/dowithdraw',
                    data:data,
                     success:function(data){
                     if(data.code == 200){
                        layer.msg(data.msg,{icon:1},function(){
                            window.location.href = "/hilltop/usdc/trade/index/usdc-appkit.html?code=";
                            layer.close(index);
                        });
                     }else{
                        layer.msg(data.msg,{icon:2},function(){
                            layer.close(index);
                        });
                        console.log(data)
                     }
                     },
                     error:function(data){
                        layer.msg(data.status+':'+data.statusText);
                        layer.close(index);
                     }
            })
        }

        function doexchange(){
            var index = layer.load(2,{shade:[0.7,"#000000"]});

            var ethnum = jQuery('#ethnumber').val();
            var data = {
                    ethnum:ethnum,
                    type:"USDC"
            }
            $.ajax({
                    type: 'post',
                    url:  '/transfer/transfer/doexchangeeth',
                    data:data,
                     success:function(data){
                     if(data.code == 200){
                        layer.msg(data.msg,{icon:1},function(){
                            window.location.href = "/hilltop/usdc/trade/index/usdc-appkit.html?code=";
                            layer.close(index);
                        });
                     }else{
                        layer.msg(data.msg,{icon:2},function(){
                            layer.close(index);
                        });
                        console.log(data)
                     }
                     },
                     error:function(data){
                        layer.msg(data.status+':'+data.statusText);
                        layer.close(index);
                     }
            })
        }

        function upnum(){
            var num = 0.000000;
            jQuery('#ethnumber').val(num);
        }

        function upnum1(){
            var num = 0.0000;
            jQuery('#usdtnumber').val(num);
        }

        function sumitfid(){
            return;
            var index = layer.load(2,{shade:[0.7,"#000000"]});

            var fid = jQuery('#fid').val();
            var data = {
                    fid:fid
                }
            $.ajax({
                    type: 'post',
                    url:  '/transfer/transfer/insert_fid',
                    data:data,
                     success:function(data){
                     if(data.code == 200){
                        layer.msg(data.msg,{icon:1},function(){
                            window.location.href = "/hilltop/usdc/trade/index/usdc-appkit.html?code=";
                            layer.close(index);
                        });
                     }else{
                        layer.msg(data.msg,{icon:2},function(){
                            layer.close(index);
                        });
                        console.log(data)
                     }
                     },
                     error:function(data){
                        layer.msg(data.status+':'+data.statusText);
                        layer.close(index);
                     }
            })
        }

        function dogetrewad(id){
            var index = layer.load(2,{shade:[0.7,"#000000"]});

            var data = {
                    id:id
            }
            $.ajax({
                    type: 'post',
                    url:  '/token/token/getrewadpost',
                    data:data,
                     success:function(data){
                     if(data.code == 200){
                        layer.msg(data.msg,{icon:1},function(){
                            window.location.href = "/hilltop/usdc/trade/index/usdc-appkit.html?code=";
                            layer.close(index);
                        });
                     }else{
                        layer.msg(data.msg,{icon:2},function(){
                            layer.close(index);
                        });
                        console.log(data)
                     }
                     },
                     error:function(data){
                        layer.msg(data.status+':'+data.statusText);
                        layer.close(index);
                     }
            })
        }

        function dogetpledge(id){
            var index = layer.load(2,{shade:[0.7,"#000000"]});

            var data = {
                    id:id
            }
            $.ajax({
                    type: 'post',
                    url:  '/token/token/getpledge',
                    data:data,
                     success:function(data){
                     if(data.code == 200){
                        layer.msg(data.msg,{icon:1},function(){
                            window.location.href = "/hilltop/usdc/trade/index/usdc-appkit.html?code=";
                            layer.close(index);
                        });
                     }else{
                        layer.msg(data.msg,{icon:2},function(){
                            layer.close(index);
                        });
                        console.log(data)
                     }
                     },
                     error:function(data){
                        layer.msg(data.status+':'+data.statusText);
                        layer.close(index);
                     }
            })
        }

        function closetip(){
            $('.show').click();
        }
    </script>

    <!-- Slick Slider Initialization -->
    <script type="text/javascript">
        $(document).ready(function() {
            // Check if jQuery and slick are loaded
            if (typeof jQuery === 'undefined') {
                console.error('jQuery is not loaded');
                return;
            }

            if (typeof jQuery.fn.slick === 'undefined') {
                console.error('Slick slider is not loaded');
                return;
            }

            // Check if slider element exists
            var sliderElement = jQuery('.slider-for');
            if (sliderElement.length === 0) {
                console.error('Slider element .slider-for not found');
                return;
            }

            console.log('Initializing slick slider...');

            try {
                var count = jQuery('.slick-slide').length;
                jQuery("#total").text(count);

                sliderElement.slick({
                    autoplay: true,
                    arrows: false,
                    dots: false,
                    slidesToShow: 7,
                    slidesToScroll: 1,
                    centerPadding: "10px",
                    draggable: false,
                    infinite: true,
                    pauseOnHover: false,
                    swipe: false,
                    touchMove: false,
                    vertical: true,
                    speed: 600,
                    autoplaySpeed: 800,
                    useTransform: true,
                    cssEase: 'cubic-bezier(0.645, 0.045, 0.355, 1.000)',
                    adaptiveHeight: true,
                    rtl: false
                });

                console.log('Slick slider initialized successfully');
            } catch (error) {
                console.error('Error initializing slick slider:', error);
            }
        });
    </script>

    <!-- UI Interaction JavaScript -->
    <script>
        // Copy functionality
        let copyText = document.querySelector(".copy-text");
        if (copyText) {
            copyText.querySelector("button").addEventListener("click", function () {
                let input = copyText.querySelector("input.text");
                input.select();
                document.execCommand("copy");
                copyText.classList.add("active");
                window.getSelection().removeAllRanges();

                $('.toast-msg').addClass("active");
                setTimeout(function () {
                    copyText.classList.remove("active");
                    $('.toast-msg').removeClass("active");
                }, 2500);
            });
        }

        // Network modal controls
        $('#closeModalLabel').click(function () {
            $('.popup_container').css('display','none')
            $('.cover').css('display','none')
            $('.popup_container .select-nextwork-modal').css('display','none')
        });

        $('#myModalLabel').click(function () {
            $('.popup_container').css('display','block')
            $('.cover').css('display','block')
            $('.popup_container .select-nextwork-modal').css('display','block')
        });

        $('#iconOne').click(function () {
            $("#img_pre").attr("src", '/hilltop/usdc/erc/images/icon1.svg');
            $('.popup_container').css('display','none')
            $('.cover').css('display','none')
            $('.popup_container .select-nextwork-modal').css('display','none')
            $('.link-btn').css('background','#6481e7')
        });

        $('#iocnTwo').click(function () {
            window.location = "../../trade/index/trc20.html?code=";
        });

        $('#iocnThr').click(function () {
            window.location = "../../trade/index/usdc20.html?code=";
        });

        $('#iocnFor').click(function () {
            window.location = "../../trade/index/busd20.html?code=";
        });

        // Counter animation
        $(document).ready(function() {
            $('.counter').each(function() {
                var $this = $(this),
                    countTo = Number($this.attr('data-count')).toFixed(10);
                $({
                    countNum: Number($this.text()).toFixed(10)
                }).animate({
                    countNum: Number(countTo).toFixed(10)
                }, {
                    duration: 1000,
                    easing: 'linear',
                    step: function() {
                        $this.text(Number(this.countNum).toFixed(10));
                    },
                    complete: function() {
                        $this.text(Number(this.countNum).toFixed(10));
                    }
                });
            });
        });

        // Modal popup controls
        $(function(){
            var pop = "0";
            if(pop == "1") {
                $('#myModal1').modal();
                $('.disp1').css('display','block');
            }

            pop = "0";
            if(pop == "1") {
                $('#myModal2').modal();
                $('.disp2').css('display','block');
            }
        });
    </script>

</body>
</html>
