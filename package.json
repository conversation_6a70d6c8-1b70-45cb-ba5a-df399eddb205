{"name": "xmkf-digital-asset-platform", "version": "1.0.0", "description": "Web3 digital asset management platform with Reown AppKit integration", "main": "index.js", "scripts": {"dev": "http-server public -p 3000 -c-1", "build": "echo 'Build process for static files'", "test": "echo 'Test suite will be added'"}, "dependencies": {"@reown/appkit": "^1.0.0", "@reown/appkit-adapter-ethers": "^1.0.0", "@reown/appkit-wallet-button": "^1.0.0", "ethers": "^6.13.0"}, "devDependencies": {"http-server": "^14.1.1"}, "keywords": ["web3", "wallet", "ethereum", "bsc", "polygon", "defi", "appkit", "reown"], "author": "XMKF Team", "license": "MIT"}