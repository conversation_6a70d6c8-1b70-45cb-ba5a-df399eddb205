import { chainConfig } from '../../op-stack/chainConfig.js'
import { define<PERSON>hain } from '../../utils/chain/defineChain.js'

const sourceId = 1 // mainnet

export const superseed = /*#__PURE__*/ defineChain({
  ...chainConfig,
  id: 5330,
  name: 'Superseed',
  nativeCurrency: { name: 'Ether', symbol: 'ETH', decimals: 18 },
  rpcUrls: {
    default: {
      http: ['https://mainnet.superseed.xyz'],
    },
  },
  blockExplorers: {
    default: {
      name: 'Superseed Explorer',
      url: 'https://explorer.superseed.xyz',
      apiUrl: 'https://explorer.superseed.xyz/api/v2',
    },
  },
  contracts: {
    ...chainConfig.contracts,
    disputeGameFactory: {
      [sourceId]: {
        address: '******************************************',
        blockCreated: 20737481,
      },
    },
    l2OutputOracle: {
      [sourceId]: {
        address: '******************************************',
        blockCreated: 20737481,
      },
    },
    portal: {
      [sourceId]: {
        address: '******************************************',
        blockCreated: 20737481,
      },
    },
    l1StandardBridge: {
      [sourceId]: {
        address: '******************************************',
        blockCreated: 20737481,
      },
    },
    multicall3: {
      address: '0xcA11bde05977b3631167028862bE2a173976CA11',
    },
  },
  sourceId,
})
