export declare const plumeMainnet: {
    blockExplorers: {
        readonly default: {
            readonly name: "Blockscout";
            readonly url: "https://explorer.plume.org";
            readonly apiUrl: "https://explorer.plume.org/api";
        };
    };
    blockTime?: number | undefined | undefined;
    contracts: {
        readonly multicall3: {
            readonly address: "0xcA11bde05977b3631167028862bE2a173976CA11";
            readonly blockCreated: 39679;
        };
    };
    ensTlds?: readonly string[] | undefined;
    id: 98866;
    name: "Plume";
    nativeCurrency: {
        readonly name: "Plume";
        readonly symbol: "PLUME";
        readonly decimals: 18;
    };
    rpcUrls: {
        readonly default: {
            readonly http: readonly ["https://rpc.plume.org"];
            readonly webSocket: readonly ["wss://rpc.plume.org"];
        };
    };
    sourceId: 1;
    testnet?: boolean | undefined | undefined;
    custom?: Record<string, unknown> | undefined;
    fees?: import("../../index.js").ChainFees<undefined> | undefined;
    formatters?: undefined;
    serializers?: import("../../index.js").ChainSerializers<undefined, import("../../index.js").TransactionSerializable> | undefined;
};
//# sourceMappingURL=plumeMainnet.d.ts.map