import { define<PERSON><PERSON><PERSON> } from '../../utils/chain/defineChain.js'

export const sonicTestnet = /*#__PURE__*/ define<PERSON>hain({
  id: 64_165,
  name: 'Sonic Testnet',
  nativeCurrency: {
    decimals: 18,
    name: '<PERSON>',
    symbol: 'S',
  },
  rpcUrls: {
    default: { http: ['https://rpc.testnet.soniclabs.com'] },
  },
  blockExplorers: {
    default: {
      name: 'Sonic Testnet Explorer',
      url: 'https://testnet.soniclabs.com/',
    },
  },
  testnet: true,
})
