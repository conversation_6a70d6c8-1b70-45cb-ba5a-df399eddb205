@echo off
echo ========================================
echo   XMKF Digital Asset Platform
echo   AppKit Dependencies Installation
echo ========================================
echo.

echo [1/4] Checking Node.js installation...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed!
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)
echo ✓ Node.js is installed

echo.
echo [2/4] Checking npm installation...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: npm is not available!
    pause
    exit /b 1
)
echo ✓ npm is available

echo.
echo [3/4] Installing AppKit dependencies...
echo Installing @reown/appkit...
npm install @reown/appkit@latest

echo Installing @reown/appkit-adapter-ethers...
npm install @reown/appkit-adapter-ethers@latest

echo Installing @reown/appkit-wallet-button...
npm install @reown/appkit-wallet-button@latest

echo Installing ethers.js...
npm install ethers@^6.13.0

echo Installing development dependencies...
npm install http-server@latest --save-dev

echo.
echo [4/4] Verifying installation...
if exist "node_modules\@reown\appkit" (
    echo ✓ @reown/appkit installed successfully
) else (
    echo ✗ @reown/appkit installation failed
)

if exist "node_modules\@reown\appkit-adapter-ethers" (
    echo ✓ @reown/appkit-adapter-ethers installed successfully
) else (
    echo ✗ @reown/appkit-adapter-ethers installation failed
)

if exist "node_modules\@reown\appkit-wallet-button" (
    echo ✓ @reown/appkit-wallet-button installed successfully
) else (
    echo ✗ @reown/appkit-wallet-button installation failed
)

if exist "node_modules\ethers" (
    echo ✓ ethers.js installed successfully
) else (
    echo ✗ ethers.js installation failed
)

echo.
echo ========================================
echo   Installation Complete!
echo ========================================
echo.
echo Next steps:
echo 1. Get your Project ID from https://cloud.reown.com
echo 2. Update the configuration files
echo 3. Start development server with: npm run dev
echo.
pause
