{"name": "@reown/appkit-utils", "version": "1.7.11", "sideEffects": false, "type": "module", "main": "./dist/esm/exports/index.js", "types": "./dist/types/exports/index.d.ts", "files": ["dist", "!tsconfig.tsbuildinfo", "README.md"], "exports": {".": {"types": "./dist/types/exports/index.d.ts", "import": "./dist/esm/exports/index.js", "default": "./dist/esm/exports/index.js"}, "./ethers": {"types": "./dist/types/exports/ethers.d.ts", "import": "./dist/esm/exports/ethers.js", "default": "./dist/esm/exports/ethers.js"}, "./solana": {"types": "./dist/types/exports/solana.d.ts", "import": "./dist/esm/exports/solana.js", "default": "./dist/esm/exports/solana.js"}, "./wallet-standard": {"types": "./dist/types/exports/wallet-standard.d.ts", "import": "./dist/esm/exports/wallet-standard.js", "default": "./dist/esm/exports/wallet-standard.js"}, "./bitcoin": {"types": "./dist/types/exports/bitcoin.d.ts", "import": "./dist/esm/exports/bitcoin.js", "default": "./dist/esm/exports/bitcoin.js"}}, "typesVersions": {"*": {"ethers": ["./dist/types/exports/ethers.d.ts"], "solana": ["./dist/types/exports/solana.d.ts"]}}, "dependencies": {"@wallet-standard/wallet": "1.1.0", "@walletconnect/logger": "2.1.2", "@walletconnect/universal-provider": "2.21.3", "valtio": "1.13.2", "viem": ">=2.31.3", "@reown/appkit-common": "1.7.11", "@reown/appkit-controllers": "1.7.11", "@reown/appkit-polyfills": "1.7.11", "@reown/appkit-wallet": "1.7.11"}, "devDependencies": {"bs58": "6.0.0", "@solana/web3.js": "1.98.0", "@wallet-standard/features": "1.1.0", "@wallet-standard/base": "1.1.0", "@solana/wallet-standard-features": "1.3.0", "@coinbase/wallet-sdk": "4.3.0", "@safe-global/safe-apps-provider": "0.18.6", "@solana/wallet-adapter-base": "0.9.26", "@vitest/coverage-v8": "2.1.9", "vitest": "3.1.3", "@walletconnect/types": "2.21.2"}, "peerDependencies": {"valtio": "1.13.2"}, "author": "Reown (https://discord.gg/reown)", "license": "Apache-2.0", "homepage": "https://github.com/reown-com/appkit", "repository": {"type": "git", "url": "git+https://github.com/reown-com/appkit.git"}, "bugs": {"url": "https://github.com/reown-com/appkit/issues"}, "keywords": ["appkit", "wallet", "onboarding", "reown", "dapps", "web3", "wagmi", "ethereum", "solana", "bitcoin"], "scripts": {"build:clean": "rm -rf dist", "build": "tsc --build", "watch": "tsc --watch", "typecheck": "tsc --noEmit", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "test": "vitest run --dir tests --coverage.enabled=true --coverage.reporter=json --coverage.reporter=json-summary --coverage.reportOnFailure=true"}}