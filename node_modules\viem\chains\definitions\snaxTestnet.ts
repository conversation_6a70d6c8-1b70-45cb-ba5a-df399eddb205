import { chainConfig } from '../../op-stack/chainConfig.js'
import { define<PERSON>hain } from '../../utils/chain/defineChain.js'

const sourceId = 11_155_111 // sepolia

export const snaxTestnet = /*#__PURE__*/ defineChain({
  ...chainConfig,
  id: 13001,
  network: 'snaxchain-testnet',
  name: 'SnaxChain Testnet',
  nativeCurrency: { name: '<PERSON><PERSON> Ether', symbol: 'ETH', decimals: 18 },
  rpcUrls: {
    default: {
      http: ['https://testnet.snaxchain.io'],
    },
  },
  blockExplorers: {
    default: {
      name: 'Snax Explorer',
      url: 'https://testnet-explorer.snaxchain.io',
      apiUrl: 'https://testnet-explorer.snaxchain.io/api',
    },
  },
  contracts: {
    ...chainConfig.contracts,
    disputeGameFactory: {
      [sourceId]: {
        address: '******************************************',
      },
    },
    l2OutputOracle: {
      [sourceId]: {
        address: '******************************************',
      },
    },
    multicall3: {
      address: '******************************************',
    },
    portal: {
      [sourceId]: {
        address: '******************************************',
      },
    },
    l1StandardBridge: {
      [sourceId]: {
        address: '******************************************',
      },
    },
  },
  testnet: true,
  sourceId,
})
