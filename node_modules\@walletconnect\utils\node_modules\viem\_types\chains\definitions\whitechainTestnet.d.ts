export declare const whitechainTestnet: {
    blockExplorers: {
        readonly default: {
            readonly name: "Whitechain Explorer";
            readonly url: "https://testnet.whitechain.io";
        };
    };
    blockTime?: number | undefined | undefined;
    contracts?: {
        [x: string]: import("../../index.js").ChainContract | {
            [sourceId: number]: import("../../index.js").ChainContract | undefined;
        } | undefined;
        ensRegistry?: import("../../index.js").ChainContract | undefined;
        ensUniversalResolver?: import("../../index.js").ChainContract | undefined;
        multicall3?: import("../../index.js").ChainContract | undefined;
        universalSignatureVerifier?: import("../../index.js").ChainContract | undefined;
    } | undefined;
    ensTlds?: readonly string[] | undefined;
    id: 2625;
    name: "Whitechain Testnet";
    nativeCurrency: {
        readonly decimals: 18;
        readonly name: "WhiteBIT Coin";
        readonly symbol: "WBT";
    };
    rpcUrls: {
        readonly default: {
            readonly http: readonly ["https://rpc-testnet.whitechain.io"];
        };
    };
    sourceId?: number | undefined | undefined;
    testnet: true;
    custom?: Record<string, unknown> | undefined;
    fees?: import("../../index.js").ChainFees<undefined> | undefined;
    formatters?: undefined;
    serializers?: import("../../index.js").ChainSerializers<undefined, import("../../index.js").TransactionSerializable> | undefined;
};
//# sourceMappingURL=whitechainTestnet.d.ts.map