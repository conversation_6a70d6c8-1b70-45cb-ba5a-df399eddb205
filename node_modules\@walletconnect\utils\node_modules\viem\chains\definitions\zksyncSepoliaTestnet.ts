import { define<PERSON>hain } from '../../utils/chain/defineChain.js'
import { chainConfig } from '../../zksync/chainConfig.js'

export const zksyncSepoliaTestnet = /*#__PURE__*/ define<PERSON>hain({
  ...chainConfig,
  id: 300,
  name: 'ZKsync Sepolia Testnet',
  network: 'zksync-sepolia-testnet',
  nativeCurrency: { name: 'Ether', symbol: 'ETH', decimals: 18 },
  rpcUrls: {
    default: {
      http: ['https://sepolia.era.zksync.dev'],
      webSocket: ['wss://sepolia.era.zksync.dev/ws'],
    },
  },
  blockExplorers: {
    default: {
      name: 'Etherscan',
      url: 'https://sepolia-era.zksync.network/',
      apiUrl: 'https://api-sepolia-era.zksync.network/api',
    },
    native: {
      name: 'ZKsync Explorer',
      url: 'https://sepolia.explorer.zksync.io/',
      blockExplorerApi: 'https://block-explorer-api.sepolia.zksync.dev/api',
    },
  },
  contracts: {
    multicall3: {
      address: '******************************************',
    },
    universalSignatureVerifier: {
      address: '******************************************',
      blockCreated: 3855712,
    },
  },
  testnet: true,
})
