import { CoinbaseWalletSDK } from './CoinbaseWalletSDK.js';
export default CoinbaseWalletSDK;
export type { CoinbaseWalletProvider } from './CoinbaseWalletProvider.js';
export { CoinbaseWalletSDK } from './CoinbaseWalletSDK.js';
export { createCoinbaseWalletSDK } from './createCoinbaseWalletSDK.js';
export type { AppMetadata, Preference, ProviderInterface } from './core/provider/interface.js';
//# sourceMappingURL=index.d.ts.map