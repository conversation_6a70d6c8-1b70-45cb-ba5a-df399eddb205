import { chainConfig } from '../../op-stack/chainConfig.js'
import { define<PERSON>hain } from '../../utils/chain/defineChain.js'

const sourceId = 11155111 // sepolia

export const superseedSepolia = /*#__PURE__*/ defineChain({
  ...chainConfig,
  id: 53302,
  name: 'Superseed Sepolia',
  nativeCurrency: {
    name: 'Ether',
    symbol: 'ETH',
    decimals: 18,
  },
  rpcUrls: {
    default: {
      http: ['https://sepolia.superseed.xyz'],
    },
  },
  blockExplorers: {
    default: {
      name: 'Superseed Sepolia Explorer',
      url: 'https://sepolia-explorer.superseed.xyz',
      apiUrl: 'https://sepolia-explorer.superseed.xyz/api/v2',
    },
  },
  contracts: {
    ...chainConfig.contracts,
    multicall3: {
      address: '******************************************',
    },
    portal: {
      [sourceId]: {
        address: '******************************************',
        blockCreated: 5523438,
      },
    },
    l1StandardBridge: {
      [sourceId]: {
        address: '******************************************',
        blockCreated: 5523442,
      },
    },
  },
  testnet: true,
  sourceId,
})
