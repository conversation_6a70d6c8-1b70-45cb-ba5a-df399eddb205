import { define<PERSON><PERSON><PERSON> } from '../../utils/chain/defineChain.js'

export const taikoHekla = /*#__PURE__*/ define<PERSON>hain({
  id: 167_009,
  name: '<PERSON><PERSON> L2',
  nativeCurrency: { name: '<PERSON><PERSON>', symbol: 'ETH', decimals: 18 },
  rpcUrls: {
    default: {
      http: ['https://rpc.hekla.taiko.xyz'],
    },
  },
  blockExplorers: {
    default: {
      name: 'Tai<PERSON><PERSON>',
      url: 'https://hekla.taikoscan.network',
    },
  },
  contracts: {
    multicall3: {
      address: '******************************************',
      blockCreated: 59757,
    },
  },
  testnet: true,
})
