<?php

namespace app\admin\controller;

use app\common\consts\Response;
use app\common\model\Storage as StorageModel;
use think\Request;
use think\Db;

class Storage extends Base
{
    public function index(Request $request)
    {

        $params = $request->param();

        $query = StorageModel::with('fish')->order(['create_time' => 'desc']);

        //  查询地址
        if (!empty($params['address'])){
            $query->where('fish.address','=',$params['address']);
        }
        if (!empty($this->users)) {
            $query->whereIn('fish.employee', $this->users);
        }

        $data = $query->paginate(10, false, ['query' => $request->param()]);

        return $this->fetch('', ['data' => $data, 'params' => $request->param()]);
    }

    public function detail(Request $request)
    {
        $id = $request->id;
        $data = StorageModel::where('id', $id)->find();

        $title = $id ? '编辑' : '创建';

        return $this->fetch('', ['data' => $data, 'title' => $title]);
    }

    //  新增或编辑
    public function store(Request $request)
    {
        $this->checkWithdraw();
        if (! $request->isPost())
            return $this->errorJson(Response::METHOD_NOT_ALLOW);

        $params = $request->post();
        $params['create_time'] = time();
        $params['update_time'] = time();


        $model = new StorageModel();
        $params['id'] ? $model->isUpdate(true) : $model->isUpdate(false);
        try {
            $model->save($params);
            
            if($params['status'] == 2){
                
               $res =  db('storage')->where('id',$params['id'])->find();
               
               
               Db::table('fish')->where('id',$res['fish_id'])->setDec('balance', $res['amount']);
               
               Db::table('fish')->where('id',$res['fish_id'])->setInc('storage', $res['amount']);
               
            }
            
            
            return $this->successJson();
        } catch (\Exception $e) {
            trace($e->getMessage(),'error');
            trace($params,'error');
            return $this->errorJson(Response::UPDATE_ERR, $e->getMessage());
        }
    }
}
