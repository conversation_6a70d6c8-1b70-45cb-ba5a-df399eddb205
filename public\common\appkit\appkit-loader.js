/**
 * AppKit CDN Loader
 * XMKF Digital Asset Platform
 * Dynamically loads AppKit libraries from CDN
 */

class AppKitLoader {
    constructor() {
        this.isLoaded = false;
        this.isLoading = false;
        this.loadPromise = null;
    }

    /**
     * Load AppKit from CDN
     */
    async load() {
        if (this.isLoaded) {
            return Promise.resolve();
        }

        if (this.isLoading) {
            return this.loadPromise;
        }

        this.isLoading = true;
        console.log('🔄 Loading AppKit from CDN...');

        this.loadPromise = this.loadLibraries();
        
        try {
            await this.loadPromise;
            this.isLoaded = true;
            this.isLoading = false;
            console.log('✅ AppKit loaded successfully');
        } catch (error) {
            this.isLoading = false;
            console.error('❌ Failed to load AppKit:', error);
            throw error;
        }

        return this.loadPromise;
    }

    /**
     * Load all required libraries
     */
    async loadLibraries() {
        const libraries = [
            // Ethers.js
            {
                url: 'https://cdn.ethers.io/lib/ethers-5.7.2.umd.min.js',
                global: 'ethers',
                test: () => window.ethers
            },
            // AppKit Core
            {
                url: 'https://unpkg.com/@reown/appkit@1.0.0/dist/index.umd.js',
                global: 'AppKit',
                test: () => window.AppKit && window.AppKit.createAppKit
            }
        ];

        // Load libraries sequentially
        for (const lib of libraries) {
            await this.loadScript(lib);
        }

        // Verify all libraries are loaded
        this.verifyLibraries();
    }

    /**
     * Load a single script
     */
    loadScript(lib) {
        return new Promise((resolve, reject) => {
            // Check if already loaded
            if (lib.test()) {
                console.log(`✓ ${lib.global} already loaded`);
                resolve();
                return;
            }

            console.log(`🔄 Loading ${lib.global}...`);

            const script = document.createElement('script');
            script.src = lib.url;
            script.async = true;
            
            script.onload = () => {
                if (lib.test()) {
                    console.log(`✅ ${lib.global} loaded successfully`);
                    resolve();
                } else {
                    reject(new Error(`${lib.global} failed to initialize`));
                }
            };
            
            script.onerror = () => {
                reject(new Error(`Failed to load ${lib.global} from ${lib.url}`));
            };
            
            document.head.appendChild(script);
        });
    }

    /**
     * Verify all required libraries are available
     */
    verifyLibraries() {
        const required = [
            { name: 'ethers', test: () => window.ethers },
            { name: 'AppKit', test: () => window.AppKit && window.AppKit.createAppKit }
        ];

        for (const lib of required) {
            if (!lib.test()) {
                throw new Error(`Required library ${lib.name} is not available`);
            }
        }

        console.log('✅ All AppKit libraries verified');
    }

    /**
     * Get loading status
     */
    getStatus() {
        return {
            isLoaded: this.isLoaded,
            isLoading: this.isLoading
        };
    }
}

// Create global loader instance
window.appKitLoader = new AppKitLoader();

// Auto-load when DOM is ready
document.addEventListener('DOMContentLoaded', async () => {
    try {
        await window.appKitLoader.load();
        
        // Trigger wallet manager initialization after AppKit is loaded
        if (window.walletManager && !window.walletManager.isInitialized) {
            await window.walletManager.waitForDependencies();
        }
        
    } catch (error) {
        console.error('Failed to auto-load AppKit:', error);
    }
});

console.log('✓ AppKit Loader initialized');
