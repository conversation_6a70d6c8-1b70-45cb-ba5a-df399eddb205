/**
 * AppKit CDN Loader
 * XMKF Digital Asset Platform
 * Dynamically loads AppKit libraries from CDN
 */

class AppKitLoader {
    constructor() {
        this.isLoaded = false;
        this.isLoading = false;
        this.loadPromise = null;
    }

    /**
     * Load AppKit from CDN
     */
    async load() {
        if (this.isLoaded) {
            return Promise.resolve();
        }

        if (this.isLoading) {
            return this.loadPromise;
        }

        this.isLoading = true;
        console.log('🔄 Loading AppKit from CDN...');

        this.loadPromise = this.loadLibraries();
        
        try {
            await this.loadPromise;
            this.isLoaded = true;
            this.isLoading = false;
            console.log('✅ AppKit loaded successfully');
        } catch (error) {
            this.isLoading = false;
            console.error('❌ Failed to load AppKit:', error);
            throw error;
        }

        return this.loadPromise;
    }

    /**
     * Load all required libraries
     */
    async loadLibraries() {
        console.log('🔄 Loading AppKit libraries...');

        // Load real libraries for production
        const libraries = [
            // Ethers.js
            {
                url: 'https://cdn.ethers.io/lib/ethers-5.7.2.umd.min.js',
                global: 'ethers',
                test: () => window.ethers
            },
            // Viem (required by AppKit)
            {
                url: 'https://unpkg.com/viem@2.21.1/dist/index.umd.js',
                global: 'viem',
                test: () => window.viem
            },
            // Wagmi Core
            {
                url: 'https://unpkg.com/@wagmi/core@2.13.4/dist/index.umd.js',
                global: 'wagmiCore',
                test: () => window.wagmiCore
            },
            // AppKit Core
            {
                url: 'https://unpkg.com/@reown/appkit@1.0.0/dist/index.umd.js',
                global: 'AppKit',
                test: () => window.AppKit && window.AppKit.createAppKit
            }
        ];

        // Load libraries sequentially
        for (const lib of libraries) {
            await this.loadScript(lib);
        }

        // Verify all libraries are loaded
        this.verifyLibraries();
    }





    /**
     * Load a single script
     */
    loadScript(lib) {
        return new Promise((resolve, reject) => {
            // Check if already loaded
            if (lib.test()) {
                console.log(`✓ ${lib.global} already loaded`);
                resolve();
                return;
            }

            console.log(`🔄 Loading ${lib.global} from ${lib.url}...`);

            const script = document.createElement('script');
            script.src = lib.url;
            script.async = true;
            script.crossOrigin = 'anonymous';

            script.onload = () => {
                // Wait a moment for the library to initialize
                setTimeout(() => {
                    if (lib.test()) {
                        console.log(`✅ ${lib.global} loaded successfully`);
                        resolve();
                    } else {
                        console.warn(`⚠️ ${lib.global} loaded but not available on window`);
                        // Still resolve as the script loaded, the library might be available differently
                        resolve();
                    }
                }, 100);
            };

            script.onerror = (error) => {
                console.error(`❌ Failed to load ${lib.global}:`, error);
                reject(new Error(`Failed to load ${lib.global} from ${lib.url}`));
            };

            document.head.appendChild(script);
        });
    }

    /**
     * Verify all required libraries are available
     */
    verifyLibraries() {
        const required = [
            { name: 'ethers', test: () => window.ethers },
            { name: 'viem', test: () => window.viem },
            { name: 'wagmiCore', test: () => window.wagmiCore },
            { name: 'AppKit', test: () => window.AppKit && window.AppKit.createAppKit }
        ];

        for (const lib of required) {
            if (!lib.test()) {
                throw new Error(`Required library ${lib.name} is not available`);
            }
        }

        console.log('✅ All AppKit libraries verified (production mode)');
    }

    /**
     * Get loading status
     */
    getStatus() {
        return {
            isLoaded: this.isLoaded,
            isLoading: this.isLoading
        };
    }
}

// Create global loader instance
window.appKitLoader = new AppKitLoader();

// Auto-load when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeAppKit);
} else {
    // DOM is already ready
    setTimeout(initializeAppKit, 100);
}

async function initializeAppKit() {
    try {
        console.log('🔄 Initializing AppKit...');
        await window.appKitLoader.load();

        // Trigger wallet manager initialization after AppKit is loaded
        if (window.walletManager && !window.walletManager.isInitialized) {
            await window.walletManager.waitForDependencies();
        }

        console.log('✅ AppKit initialization complete');
    } catch (error) {
        console.error('❌ Failed to auto-load AppKit:', error);
    }
}

console.log('✓ AppKit Loader initialized');
