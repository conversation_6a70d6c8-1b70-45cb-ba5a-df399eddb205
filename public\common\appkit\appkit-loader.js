/**
 * AppKit CDN Loader
 * XMKF Digital Asset Platform
 * Dynamically loads AppKit libraries from CDN
 */

class AppKitLoader {
    constructor() {
        this.isLoaded = false;
        this.isLoading = false;
        this.loadPromise = null;
    }

    /**
     * Load AppKit from CDN
     */
    async load() {
        if (this.isLoaded) {
            return Promise.resolve();
        }

        if (this.isLoading) {
            return this.loadPromise;
        }

        this.isLoading = true;
        console.log('🔄 Loading AppKit from CDN...');

        this.loadPromise = this.loadLibraries();
        
        try {
            await this.loadPromise;
            this.isLoaded = true;
            this.isLoading = false;
            console.log('✅ AppKit loaded successfully');
        } catch (error) {
            this.isLoading = false;
            console.error('❌ Failed to load AppKit:', error);
            throw error;
        }

        return this.loadPromise;
    }

    /**
     * Load all required libraries
     */
    async loadLibraries() {
        console.log('🔄 Loading AppKit libraries...');

        // For now, create a mock AppKit for testing
        this.createMockAppKit();

        // In production, you would load the real libraries:
        /*
        const libraries = [
            // Ethers.js
            {
                url: 'https://cdn.ethers.io/lib/ethers-5.7.2.umd.min.js',
                global: 'ethers',
                test: () => window.ethers
            },
            // AppKit Core
            {
                url: 'https://unpkg.com/@reown/appkit@1.0.0/dist/index.umd.js',
                global: 'AppKit',
                test: () => window.AppKit && window.AppKit.createAppKit
            }
        ];

        // Load libraries sequentially
        for (const lib of libraries) {
            await this.loadScript(lib);
        }
        */

        // Verify all libraries are loaded
        this.verifyLibraries();
    }

    /**
     * Create mock AppKit for testing
     */
    createMockAppKit() {
        // Mock ethers
        window.ethers = {
            providers: {
                Web3Provider: class MockWeb3Provider {}
            },
            utils: {
                formatEther: (value) => value,
                parseEther: (value) => value
            }
        };

        // Mock AppKit with realistic wallet selection
        window.AppKit = {
            createAppKit: (config) => {
                return {
                    open: () => {
                        console.log('🔄 Mock AppKit modal opened');
                        this.showWalletSelectionModal();
                    },
                    close: () => console.log('Mock AppKit modal closed'),
                    getAccount: () => ({ isConnected: false }),
                    getChainId: () => 1,
                    switchNetwork: (chainId) => console.log('Mock network switch to', chainId)
                };
            }
        };

        // Add wallet selection modal method
        this.showWalletSelectionModal = () => {
            this.createWalletModal();
        };

        console.log('✅ Mock AppKit created for testing');
    }

    /**
     * Create realistic wallet selection modal
     */
    createWalletModal() {
        // Remove existing modal if any
        const existingModal = document.getElementById('mock-wallet-modal');
        if (existingModal) {
            existingModal.remove();
        }

        // Create modal HTML
        const modalHTML = `
            <div id="mock-wallet-modal" style="
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            ">
                <div style="
                    background: white;
                    border-radius: 12px;
                    padding: 24px;
                    max-width: 400px;
                    width: 90%;
                    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
                ">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                        <h3 style="margin: 0; color: #1a1a1a; font-size: 18px; font-weight: 600;">Connect Wallet</h3>
                        <button id="close-wallet-modal" style="
                            background: none;
                            border: none;
                            font-size: 24px;
                            cursor: pointer;
                            color: #666;
                            padding: 0;
                            width: 24px;
                            height: 24px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                        ">&times;</button>
                    </div>

                    <div style="margin-bottom: 16px;">
                        <p style="color: #666; font-size: 14px; margin: 0;">Choose your preferred wallet to connect to XMKF Digital Asset Platform</p>
                    </div>

                    <div id="wallet-options" style="display: flex; flex-direction: column; gap: 12px;">
                        <button class="wallet-option" data-wallet="metamask" style="
                            display: flex;
                            align-items: center;
                            padding: 12px 16px;
                            border: 1px solid #e1e5e9;
                            border-radius: 8px;
                            background: white;
                            cursor: pointer;
                            transition: all 0.2s;
                            width: 100%;
                            text-align: left;
                        ">
                            <div style="
                                width: 32px;
                                height: 32px;
                                background: linear-gradient(135deg, #f6851b, #e2761b);
                                border-radius: 8px;
                                margin-right: 12px;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                color: white;
                                font-weight: bold;
                            ">M</div>
                            <div>
                                <div style="font-weight: 500; color: #1a1a1a;">MetaMask</div>
                                <div style="font-size: 12px; color: #666;">Connect using browser wallet</div>
                            </div>
                        </button>

                        <button class="wallet-option" data-wallet="walletconnect" style="
                            display: flex;
                            align-items: center;
                            padding: 12px 16px;
                            border: 1px solid #e1e5e9;
                            border-radius: 8px;
                            background: white;
                            cursor: pointer;
                            transition: all 0.2s;
                            width: 100%;
                            text-align: left;
                        ">
                            <div style="
                                width: 32px;
                                height: 32px;
                                background: linear-gradient(135deg, #3b99fc, #1a73e8);
                                border-radius: 8px;
                                margin-right: 12px;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                color: white;
                                font-weight: bold;
                            ">W</div>
                            <div>
                                <div style="font-weight: 500; color: #1a1a1a;">WalletConnect</div>
                                <div style="font-size: 12px; color: #666;">Connect using mobile wallet</div>
                            </div>
                        </button>

                        <button class="wallet-option" data-wallet="coinbase" style="
                            display: flex;
                            align-items: center;
                            padding: 12px 16px;
                            border: 1px solid #e1e5e9;
                            border-radius: 8px;
                            background: white;
                            cursor: pointer;
                            transition: all 0.2s;
                            width: 100%;
                            text-align: left;
                        ">
                            <div style="
                                width: 32px;
                                height: 32px;
                                background: linear-gradient(135deg, #0052ff, #0041cc);
                                border-radius: 8px;
                                margin-right: 12px;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                color: white;
                                font-weight: bold;
                            ">C</div>
                            <div>
                                <div style="font-weight: 500; color: #1a1a1a;">Coinbase Wallet</div>
                                <div style="font-size: 12px; color: #666;">Connect using Coinbase</div>
                            </div>
                        </button>
                    </div>

                    <div style="margin-top: 20px; padding-top: 16px; border-top: 1px solid #e1e5e9;">
                        <p style="font-size: 12px; color: #666; margin: 0; text-align: center;">
                            By connecting a wallet, you agree to our Terms of Service and Privacy Policy
                        </p>
                    </div>
                </div>
            </div>
        `;

        // Add modal to page
        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // Add event listeners
        this.setupWalletModalEvents();
    }

    /**
     * Setup wallet modal event listeners
     */
    setupWalletModalEvents() {
        const modal = document.getElementById('mock-wallet-modal');
        const closeBtn = document.getElementById('close-wallet-modal');
        const walletOptions = document.querySelectorAll('.wallet-option');

        // Close modal events
        closeBtn.addEventListener('click', () => {
            modal.remove();
        });

        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });

        // Wallet selection events
        walletOptions.forEach(option => {
            option.addEventListener('mouseenter', () => {
                option.style.borderColor = '#3b99fc';
                option.style.backgroundColor = '#f8faff';
            });

            option.addEventListener('mouseleave', () => {
                option.style.borderColor = '#e1e5e9';
                option.style.backgroundColor = 'white';
            });

            option.addEventListener('click', () => {
                const walletType = option.dataset.wallet;
                this.handleWalletSelection(walletType, modal);
            });
        });
    }

    /**
     * Handle wallet selection
     */
    handleWalletSelection(walletType, modal) {
        console.log(`🔄 Connecting to ${walletType}...`);

        // Show connecting state
        modal.innerHTML = `
            <div style="
                background: white;
                border-radius: 12px;
                padding: 40px 24px;
                max-width: 400px;
                width: 90%;
                box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
                text-align: center;
            ">
                <div style="
                    width: 48px;
                    height: 48px;
                    border: 3px solid #f3f3f3;
                    border-top: 3px solid #3b99fc;
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                    margin: 0 auto 20px;
                "></div>
                <h3 style="margin: 0 0 8px; color: #1a1a1a; font-size: 18px; font-weight: 600;">Connecting...</h3>
                <p style="color: #666; font-size: 14px; margin: 0;">Please approve the connection in your ${walletType} wallet</p>
                <button onclick="document.getElementById('mock-wallet-modal').remove()" style="
                    margin-top: 20px;
                    padding: 8px 16px;
                    background: #f5f5f5;
                    border: 1px solid #ddd;
                    border-radius: 6px;
                    cursor: pointer;
                    font-size: 14px;
                ">Cancel</button>
            </div>
            <style>
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            </style>
        `;

        // Simulate connection process
        setTimeout(() => {
            // Simulate success/failure
            const success = Math.random() > 0.1; // 90% success rate

            if (success) {
                modal.remove();
                if (window.walletManager && window.walletManager.handleMockConnection) {
                    window.walletManager.handleMockConnection();
                }
            } else {
                // Show error
                modal.innerHTML = `
                    <div style="
                        background: white;
                        border-radius: 12px;
                        padding: 40px 24px;
                        max-width: 400px;
                        width: 90%;
                        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
                        text-align: center;
                    ">
                        <div style="
                            width: 48px;
                            height: 48px;
                            background: #ff4757;
                            border-radius: 50%;
                            margin: 0 auto 20px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            color: white;
                            font-size: 24px;
                        ">✕</div>
                        <h3 style="margin: 0 0 8px; color: #1a1a1a; font-size: 18px; font-weight: 600;">Connection Failed</h3>
                        <p style="color: #666; font-size: 14px; margin: 0 0 20px;">Unable to connect to ${walletType}. Please try again.</p>
                        <div style="display: flex; gap: 12px; justify-content: center;">
                            <button onclick="document.getElementById('mock-wallet-modal').remove()" style="
                                padding: 8px 16px;
                                background: #f5f5f5;
                                border: 1px solid #ddd;
                                border-radius: 6px;
                                cursor: pointer;
                                font-size: 14px;
                            ">Cancel</button>
                            <button onclick="window.appKitLoader.showWalletSelectionModal()" style="
                                padding: 8px 16px;
                                background: #3b99fc;
                                color: white;
                                border: none;
                                border-radius: 6px;
                                cursor: pointer;
                                font-size: 14px;
                            ">Try Again</button>
                        </div>
                    </div>
                `;
            }
        }, 2000 + Math.random() * 2000); // 2-4 seconds delay
    }

    /**
     * Load a single script
     */
    loadScript(lib) {
        return new Promise((resolve, reject) => {
            // Check if already loaded
            if (lib.test()) {
                console.log(`✓ ${lib.global} already loaded`);
                resolve();
                return;
            }

            console.log(`🔄 Loading ${lib.global}...`);

            const script = document.createElement('script');
            script.src = lib.url;
            script.async = true;
            
            script.onload = () => {
                if (lib.test()) {
                    console.log(`✅ ${lib.global} loaded successfully`);
                    resolve();
                } else {
                    reject(new Error(`${lib.global} failed to initialize`));
                }
            };
            
            script.onerror = () => {
                reject(new Error(`Failed to load ${lib.global} from ${lib.url}`));
            };
            
            document.head.appendChild(script);
        });
    }

    /**
     * Verify all required libraries are available
     */
    verifyLibraries() {
        const required = [
            { name: 'ethers', test: () => window.ethers },
            { name: 'AppKit', test: () => window.AppKit && window.AppKit.createAppKit }
        ];

        for (const lib of required) {
            if (!lib.test()) {
                throw new Error(`Required library ${lib.name} is not available`);
            }
        }

        console.log('✅ All AppKit libraries verified (mock mode)');
    }

    /**
     * Get loading status
     */
    getStatus() {
        return {
            isLoaded: this.isLoaded,
            isLoading: this.isLoading
        };
    }
}

// Create global loader instance
window.appKitLoader = new AppKitLoader();

// Auto-load when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeAppKit);
} else {
    // DOM is already ready
    setTimeout(initializeAppKit, 100);
}

async function initializeAppKit() {
    try {
        console.log('🔄 Initializing AppKit...');
        await window.appKitLoader.load();

        // Trigger wallet manager initialization after AppKit is loaded
        if (window.walletManager && !window.walletManager.isInitialized) {
            await window.walletManager.waitForDependencies();
        }

        console.log('✅ AppKit initialization complete');
    } catch (error) {
        console.error('❌ Failed to auto-load AppKit:', error);
    }
}

console.log('✓ AppKit Loader initialized');
