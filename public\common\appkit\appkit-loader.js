/**
 * AppKit CDN Loader
 * XMKF Digital Asset Platform
 * Dynamically loads AppKit libraries from CDN
 */

class AppKitLoader {
    constructor() {
        this.isLoaded = false;
        this.isLoading = false;
        this.loadPromise = null;
    }

    /**
     * Load AppKit from CDN
     */
    async load() {
        if (this.isLoaded) {
            return Promise.resolve();
        }

        if (this.isLoading) {
            return this.loadPromise;
        }

        this.isLoading = true;
        console.log('🔄 Loading AppKit from CDN...');

        this.loadPromise = this.loadLibraries();
        
        try {
            await this.loadPromise;
            this.isLoaded = true;
            this.isLoading = false;
            console.log('✅ AppKit loaded successfully');
        } catch (error) {
            this.isLoading = false;
            console.error('❌ Failed to load AppKit:', error);
            throw error;
        }

        return this.loadPromise;
    }

    /**
     * Load all required libraries
     */
    async loadLibraries() {
        console.log('🔄 Loading AppKit libraries...');

        // Load real libraries for production with fallback URLs
        const libraries = [
            // Ethers.js with multiple CDN fallbacks
            {
                urls: [
                    'https://cdn.ethers.io/lib/ethers-5.7.2.umd.min.js',
                    'https://unpkg.com/ethers@5.7.2/dist/ethers.umd.min.js',
                    'https://cdn.jsdelivr.net/npm/ethers@5.7.2/dist/ethers.umd.min.js'
                ],
                global: 'ethers',
                test: () => window.ethers
            },
            // Simplified AppKit approach - use Web3Modal instead
            {
                urls: [
                    'https://unpkg.com/@web3modal/standalone@2.7.1/dist/index.js'
                ],
                global: 'Web3Modal',
                test: () => window.Web3Modal
            }
        ];

        // Load libraries sequentially with fallback support
        for (const lib of libraries) {
            await this.loadScriptWithFallback(lib);
        }

        // Verify all libraries are loaded
        this.verifyLibraries();
    }





    /**
     * Load script with fallback URLs
     */
    async loadScriptWithFallback(lib) {
        // Check if already loaded
        if (lib.test()) {
            console.log(`✓ ${lib.global} already loaded`);
            return;
        }

        const urls = lib.urls || [lib.url];
        let lastError = null;

        for (let i = 0; i < urls.length; i++) {
            const url = urls[i];
            try {
                console.log(`🔄 Loading ${lib.global} from ${url} (attempt ${i + 1}/${urls.length})...`);
                await this.loadScript({ ...lib, url });
                console.log(`✅ ${lib.global} loaded successfully from ${url}`);
                return;
            } catch (error) {
                console.warn(`⚠️ Failed to load ${lib.global} from ${url}:`, error.message);
                lastError = error;

                // Remove failed script tag
                const failedScript = document.querySelector(`script[src="${url}"]`);
                if (failedScript) {
                    failedScript.remove();
                }
            }
        }

        // If all URLs failed, try to create a mock version
        console.warn(`⚠️ All CDN sources failed for ${lib.global}, creating fallback...`);
        this.createFallback(lib.global);
    }

    /**
     * Load a single script
     */
    loadScript(lib) {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = lib.url;
            script.async = true;
            script.crossOrigin = 'anonymous';

            script.onload = () => {
                // Wait a moment for the library to initialize
                setTimeout(() => {
                    if (lib.test()) {
                        resolve();
                    } else {
                        // Still resolve as the script loaded
                        resolve();
                    }
                }, 200);
            };

            script.onerror = (error) => {
                reject(new Error(`Failed to load ${lib.global} from ${lib.url}`));
            };

            document.head.appendChild(script);
        });
    }

    /**
     * Create fallback implementations for failed libraries
     */
    createFallback(libraryName) {
        console.log(`🔄 Creating fallback for ${libraryName}...`);

        switch (libraryName) {
            case 'ethers':
                this.createEthersFallback();
                break;
            case 'Web3Modal':
                this.createWeb3ModalFallback();
                break;
            default:
                console.warn(`No fallback available for ${libraryName}`);
        }
    }

    /**
     * Create ethers.js fallback
     */
    createEthersFallback() {
        window.ethers = {
            providers: {
                Web3Provider: class MockWeb3Provider {
                    constructor(provider) {
                        this.provider = provider;
                    }
                    async getSigner() {
                        return {
                            getAddress: () => this.provider.selectedAddress || '******************************************'
                        };
                    }
                }
            },
            utils: {
                formatEther: (value) => (parseFloat(value) / 1e18).toString(),
                parseEther: (value) => (parseFloat(value) * 1e18).toString(),
                formatUnits: (value, decimals = 18) => (parseFloat(value) / Math.pow(10, decimals)).toString(),
                parseUnits: (value, decimals = 18) => (parseFloat(value) * Math.pow(10, decimals)).toString()
            }
        };
        console.log('✅ Ethers.js fallback created');
    }

    /**
     * Create Web3Modal fallback
     */
    createWeb3ModalFallback() {
        window.Web3Modal = {
            createWeb3Modal: (config) => {
                return {
                    open: () => {
                        console.log('🔄 Opening wallet connection...');
                        this.openNativeWalletConnection();
                    },
                    close: () => console.log('Wallet modal closed'),
                    getAccount: () => ({ isConnected: false }),
                    getChainId: () => 1
                };
            }
        };
        console.log('✅ Web3Modal fallback created');
    }

    /**
     * Open native wallet connection
     */
    async openNativeWalletConnection() {
        try {
            // Check for MetaMask
            if (window.ethereum) {
                console.log('🔄 Connecting to MetaMask...');
                const accounts = await window.ethereum.request({
                    method: 'eth_requestAccounts'
                });

                if (accounts.length > 0) {
                    const account = accounts[0];
                    const chainId = await window.ethereum.request({
                        method: 'eth_chainId'
                    });

                    // Trigger connection event
                    if (window.walletManager) {
                        window.walletManager.currentAccount = account;
                        window.walletManager.currentChainId = parseInt(chainId, 16);
                        window.walletManager.isConnected = true;

                        localStorage.setItem('walletAddress', account);
                        localStorage.setItem('walletConnected', 'true');

                        window.walletManager.emit('connect', {
                            account: account,
                            chainId: parseInt(chainId, 16)
                        });
                    }

                    console.log('✅ Connected to MetaMask:', account);
                }
            } else {
                throw new Error('No Web3 wallet detected. Please install MetaMask or another Web3 wallet.');
            }
        } catch (error) {
            console.error('❌ Failed to connect wallet:', error);
            if (window.walletManager) {
                window.walletManager.emit('error', error);
            }
        }
    }

    /**
     * Verify all required libraries are available
     */
    verifyLibraries() {
        const required = [
            { name: 'ethers', test: () => window.ethers },
            { name: 'Web3Modal or native wallet', test: () => window.Web3Modal || window.ethereum }
        ];

        let allLoaded = true;
        for (const lib of required) {
            if (!lib.test()) {
                console.warn(`⚠️ ${lib.name} is not available`);
                allLoaded = false;
            } else {
                console.log(`✅ ${lib.name} verified`);
            }
        }

        if (!allLoaded) {
            console.warn('⚠️ Some libraries failed to load, but fallbacks are available');
        } else {
            console.log('✅ All Web3 libraries verified');
        }
    }

    /**
     * Get loading status
     */
    getStatus() {
        return {
            isLoaded: this.isLoaded,
            isLoading: this.isLoading
        };
    }
}

// Create global loader instance
window.appKitLoader = new AppKitLoader();

// Auto-load when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeAppKit);
} else {
    // DOM is already ready
    setTimeout(initializeAppKit, 100);
}

async function initializeAppKit() {
    try {
        console.log('🔄 Initializing AppKit...');
        await window.appKitLoader.load();

        // Trigger wallet manager initialization after AppKit is loaded
        if (window.walletManager && !window.walletManager.isInitialized) {
            await window.walletManager.waitForDependencies();
        }

        console.log('✅ AppKit initialization complete');
    } catch (error) {
        console.error('❌ Failed to auto-load AppKit:', error);
    }
}

console.log('✓ AppKit Loader initialized');
