import { chainConfig } from '../../op-stack/chainConfig.js'
import { define<PERSON>hain } from '../../utils/chain/defineChain.js'

const sourceId = 1 // mainnet

export const snax = /*#__PURE__*/ defineChain({
  ...chainConfig,
  id: 2192,
  network: 'snaxchain-mainnet',
  name: '<PERSON>na<PERSON><PERSON><PERSON><PERSON>',
  nativeCurrency: { name: 'Ether', symbol: 'ETH', decimals: 18 },
  rpcUrls: {
    default: {
      http: ['https://mainnet.snaxchain.io'],
    },
  },
  blockExplorers: {
    default: {
      name: 'Snax Explorer',
      url: 'https://explorer.snaxchain.io',
      apiUrl: 'https://explorer.snaxchain.io/api',
    },
  },
  contracts: {
    ...chainConfig.contracts,
    disputeGameFactory: {
      [sourceId]: {
        address: '******************************************',
      },
    },
    l2OutputOracle: {
      [sourceId]: {
        address: '******************************************',
      },
    },
    multicall3: {
      address: '******************************************',
    },
    portal: {
      [sourceId]: {
        address: '******************************************',
      },
    },
    l1StandardBridge: {
      [sourceId]: {
        address: '******************************************',
      },
    },
  },
  sourceId,
})
