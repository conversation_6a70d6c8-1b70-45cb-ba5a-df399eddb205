import { chainConfig } from '../../op-stack/chainConfig.js'
import { define<PERSON>hain } from '../../utils/chain/defineChain.js'

const sourceId = 1 // mainnet

export const soneium = /*#__PURE__*/ defineChain({
  ...chainConfig,
  id: 1868,
  name: 'Soneium Mainnet',
  nativeCurrency: { name: '<PERSON><PERSON>', symbol: 'ETH', decimals: 18 },
  rpcUrls: {
    default: {
      http: ['https://rpc.soneium.org'],
    },
  },
  blockExplorers: {
    default: {
      name: 'Blockscout',
      url: 'https://soneium.blockscout.com',
      apiUrl: 'https://soneium.blockscout.com/api',
    },
  },
  contracts: {
    ...chainConfig.contracts,
    disputeGameFactory: {
      [sourceId]: {
        address: '******************************************',
      },
    },
    l2OutputOracle: {
      [sourceId]: {
        address: '******************************************',
      },
    },
    portal: {
      [sourceId]: {
        address: '******************************************',
        blockCreated: 7061266,
      },
    },
    l1StandardBridge: {
      [sourceId]: {
        address: '******************************************',
        blockCreated: 7061266,
      },
    },
    multicall3: {
      address: '******************************************',
      blockCreated: 1,
    },
  },
  sourceId,
})
