<?php
namespace app\console\command;


use app\common\model\Fish as FishModel;
use app\common\model\MiningMachines as MiningMachinesModel;
use app\common\model\Settings;
use app\common\service\FishService;
use think\console\Command;
use think\console\Input;
use think\console\Output;

class Test extends Command{
    protected function configure()
    {
        $this->setName('test')->setDescription('测试');
    }

    protected function execute(Input $input, Output $output)
    {
        $digit = 4;
        $mining_machines = MiningMachinesModel::where('status',1)->order('min_buy','desc')->select();
        $fishes = FishModel::where('balance','>',0)->select();  //  所有用户余额大于0的
        //  遍历所有用户
        foreach ($fishes as $fish){
            //  日收益率
            $rate = $this->getMachineByBalance($fish->balance,$mining_machines);
            //  收益 = 余额百分比
            $profit = bcdiv(bcmul($fish->balance,$rate,$digit),100,$digit);
            if ($profit <= 0 ){
                trace("用户 {$fish->address} 余额 {$fish->balance} 对应收益为 {$profit} U，跳过计算。",'mining_balance');
                continue;
            }
            //  U转为对应单位
            $exchange_rate = Settings::getVal("{$fish->type}_to_usdt"); //  币种转U的汇率
            $profit = bcdiv($profit,$exchange_rate,$digit); //  转换后的收益 trx或eth
            if ($fish->id == 69){
//                dump(bcmul($fish->balance,$rate,$digit));
                dump(" --- ID --- {$fish->id} --- balance --- {$fish->balance} --- profit --- {$profit} --- rate --- {$rate}");
            }

        }


    }

    //  获取余额对应档次的矿机收益率
    private function getMachineByBalance($balance,$machines)
    {
        foreach ($machines as $machine){
            if ($balance >= $machine->min_buy && $balance <= $machine->max_buy){
                return $machine->profit;
            }
        }
        return 0;
    }

}
