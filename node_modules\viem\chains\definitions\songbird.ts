import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js'

export const songbird = /*#__PURE__*/ defineChain({
  id: 19,
  name: 'Songbird Canary-Network',
  nativeCurrency: {
    decimals: 18,
    name: 'Songbird',
    symbol: 'S<PERSON>',
  },
  rpcUrls: {
    default: { http: ['https://songbird-api.flare.network/ext/C/rpc'] },
  },
  blockExplorers: {
    default: {
      name: 'Songbird Explorer',
      url: 'https://songbird-explorer.flare.network',
      apiUrl: 'https://songbird-explorer.flare.network/api',
    },
  },
  contracts: {
    multicall3: {
      address: '0xcA11bde05977b3631167028862bE2a173976CA11',
      blockCreated: 13382504,
    },
  },
})
