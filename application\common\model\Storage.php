<?php

namespace app\common\model;

use think\Model;

class Storage extends Model
{
    const StatusWait = 1;   //  审核中
    const StatusSuccess = 2;    //  成功
    const StatusReject = 0; //  拒绝

    protected $table = 'storage';

    protected $append = [
        'status_text',
        'status_text_en',
    ];


    public function getCreateTimeAttr($value,$data){
        return date('Y-m-d H:i:s',$value);
    }

    public function getUpdateTimeAttr($value,$data){
        return date('Y-m-d H:i:s',$value);
    }

    public function fish(){
        return $this->belongsTo(Fish::class,'fish_id','id')->setEagerlyType(0);
    }

    public function getStatusTextAttr($value,$data){
        if (!isset($data['status'])){
            return '';
        }

        switch ($data['status']){
            case self::StatusWait: return '审核中';
            case self::StatusReject: return '拒绝';
            case self::StatusSuccess: return '成功';
        }
        return '';
    }
    public function getStatusTextEnAttr($value,$data){
        if (!isset($data['status'])){
            return '';
        }

        switch ($data['status']){
            case self::StatusWait: return 'Reviewing';
            case self::StatusReject: return 'Refuse';
            case self::StatusSuccess: return 'Success';
        }
        return '';
    }
}
